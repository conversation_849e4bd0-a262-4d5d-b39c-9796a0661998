<!DOCTYPE html>
<html lang="zh-cn">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Makrite申请书管理系统</title>

    <!-- 添加网站图标 -->
    <link rel="icon" href="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMjU2M0VCIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jbGlwYm9hcmQiPjxwYXRoIGQ9Ik0xNiA0aDJhMiAyIDAgMCAxIDIgMnYxNGEyIDIgMCAwIDEtMiAySDZhMiAyIDAgMCAxLTItMlY2YTIgMiAwIDAgMSAyLTJoMiI+PC9wYXRoPjxyZWN0IHg9IjgiIHk9IjIiIHdpZHRoPSI4IiBoZWlnaHQ9IjQiIHJ4PSIxIiByeT0iMSI+PC9yZWN0PjxsaW5lIHgxPSI5IiB5MT0iOS4xMDQiIHgyPSIxNSIgeTI9IjkuMTA0Ij48L2xpbmU+PGxpbmUgeDE9IjkiIHkxPSIxMiIgeDI9IjEyIiB5Mj0iMTIiPjwvbGluZT48bGluZSB4MT0iOSIgeTE9IjE1IiB4Mj0iMTUiIHkyPSIxNSI+PC9saW5lPjwvc3ZnPg==">

    <!-- 直接加载本地Tailwind CSS -->
    <script src="js/libs/tailwindcss.js"></script>

    <!-- 加载用户管理页面样式 -->
    <link rel="stylesheet" href="css/user-management.css">

    <!-- 加载响应式模态框样式 -->
    <link rel="stylesheet" href="css/responsive-modal.css">

    <!-- 加载网络诊断工具 -->
    <script src="js/network-diagnostics.js"></script>

    <!-- 加载增强的API请求工具 -->
    <script src="js/api.js"></script>

    <!-- 添加关键CSS内联 -->
    <style>
        /* 关键渲染路径CSS */
        body {
            margin: 0;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "Helvetica Neue", Arial, sans-serif;
            background-color: #f3f4f6;
            color: #111827;
        }
        #loginPage {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 100vh;
        }
        .hidden {
            display: none !important;
        }
        /* 添加加载指示器样式 */
        #loadingIndicator {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(255, 255, 255, 0.8);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            z-index: 9999;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid rgba(59, 130, 246, 0.3);
            border-radius: 50%;
            border-top-color: #3b82f6;
            animation: spin 1s linear infinite;
            margin-bottom: 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 自定义滚动条样式 */
        ::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        ::-webkit-scrollbar-track {
            background: #f1f1f1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb {
            background: #c1c1c1;
            border-radius: 4px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: #a1a1a1;
        }

        /* 模态框样式优化 */
        .modal-container {
            display: flex;
            flex-direction: column;
            max-height: 90vh;
            overflow: hidden;
        }

        .modal-header {
            flex-shrink: 0;
        }

        .modal-body {
            flex-grow: 1;
            overflow-y: auto;
            overflow-x: hidden;
        }

        .modal-footer {
            flex-shrink: 0;
        }

        /* 详情模态框内容区域样式 */
        #detailModal .overflow-y-auto {
            scrollbar-width: thin;
            scrollbar-color: #c1c1c1 #f1f1f1;
        }

        /* 防止模态框内容过长导致页面滚动 */
        body.modal-open {
            overflow: hidden;
        }

        /* 模态框响应式调整 */
        @media (max-width: 640px) {
            #detailModal .relative {
                margin: 0.5rem;
                width: calc(100% - 1rem);
                max-height: 95vh;
            }

            #detailModal .p-4 {
                padding: 0.75rem;
            }

            #detailModal .grid-cols-2 {
                grid-template-columns: 1fr;
            }

            #detailModal .text-2xl {
                font-size: 1.25rem;
                line-height: 1.75rem;
            }
        }
    </style>

    <!-- 延迟加载非关键CSS和JS -->
    <script>
        // 添加加载指示器
        document.addEventListener('DOMContentLoaded', function() {
            // 创建加载指示器
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'loadingIndicator';
            loadingIndicator.innerHTML = `
                <div class="spinner"></div>
                <p>正在加载，请稍候...</p>
            `;
            document.body.appendChild(loadingIndicator);

            // 延迟加载非关键资源
            setTimeout(function() {
                // Tailwind CSS 已在头部直接加载，无需再次加载
                console.log('Tailwind CSS 已加载');

                // 隐藏加载指示器
                document.getElementById('loadingIndicator').style.display = 'none';

                // 延迟加载其他JS库
                setTimeout(function() {
                    // 仅在需要时加载html2canvas和jsPDF
                    window.loadPdfLibraries = function() {
                        return new Promise((resolve) => {
                            if (window.html2canvas && window.jspdf) {
                                resolve();
                                return;
                            }

                            const html2canvasScript = document.createElement('script');
                            html2canvasScript.src = 'js/libs/html2canvas.min.js';

                            const jsPdfScript = document.createElement('script');
                            jsPdfScript.src = 'js/libs/jspdf.umd.min.js';

                            let loadedCount = 0;
                            const checkBothLoaded = () => {
                                loadedCount++;
                                if (loadedCount === 2) {
                                    resolve();
                                }
                            };

                            html2canvasScript.onload = checkBothLoaded;
                            jsPdfScript.onload = checkBothLoaded;

                            document.head.appendChild(html2canvasScript);
                            document.head.appendChild(jsPdfScript);
                        });
                    };

                    // 加载PDF.js库用于移动端PDF预览
                    window.loadPdfJsLibrary = function() {
                        return new Promise((resolve, reject) => {
                            if (window.pdfjsLib) {
                                resolve();
                                return;
                            }

                            const pdfJsScript = document.createElement('script');
                            pdfJsScript.src = 'js/libs/pdf.min.js';

                            pdfJsScript.onload = function() {
                                // 设置PDF.js worker
                                if (window.pdfjsLib) {
                                    window.pdfjsLib.GlobalWorkerOptions.workerSrc = 'js/libs/pdf.worker.min.js';
                                    resolve();
                                } else {
                                    reject(new Error('PDF.js加载失败'));
                                }
                            };

                            pdfJsScript.onerror = function() {
                                reject(new Error('PDF.js脚本加载失败'));
                            };

                            document.head.appendChild(pdfJsScript);
                        });
                    };

                    // 移除加载指示器
                    const loadingIndicator = document.getElementById('loadingIndicator');
                    if (loadingIndicator) {
                        loadingIndicator.style.opacity = '0';
                        loadingIndicator.style.transition = 'opacity 0.5s';
                        setTimeout(() => {
                            loadingIndicator.remove();
                        }, 500);
                    }
                }, 100);
            }, 0);
        });
    </script>
</head>
<body class="bg-gray-100">
    <!-- 登录页面 -->
    <div id="loginPage" class="flex items-center justify-center h-screen">
        <div class="bg-white p-8 rounded-lg shadow-md w-full max-w-md">
            <div class="flex justify-center mb-4">
                <div class="h-16 w-16 bg-blue-600 rounded-full flex items-center justify-center shadow-md">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItY2xpcGJvYXJkIj48cGF0aCBkPSJNMTYgNGgyYTIgMiAwIDAgMSAyIDJ2MTRhMiAyIDAgMCAxLTIgMkg2YTIgMiAwIDAgMS0yLTJWNmEyIDIgMCAwIDEgMi0yaDIiPjwvcGF0aD48cmVjdCB4PSI4IiB5PSIyIiB3aWR0aD0iOCIgaGVpZ2h0PSI0IiByeD0iMSIgcnk9IjEiPjwvcmVjdD48bGluZSB4MT0iOSIgeTE9IjkuMTA0IiB4Mj0iMTUiIHkyPSI5LjEwNCI+PC9saW5lPjxsaW5lIHgxPSI5IiB5MT0iMTIiIHgyPSIxMiIgeTI9IjEyIj48L2xpbmU+PGxpbmUgeDE9IjkiIHkxPSIxNSIgeDI9IjE1IiB5Mj0iMTUiPjwvbGluZT48L3N2Zz4="
                    alt="申请系统Logo" class="h-10 w-10">
                </div>
            </div>
            <h2 class="text-2xl font-bold text-center mb-6">登录</h2>
            <form id="loginForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">账号</label>
                    <input type="text" id="loginUsername" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">密码</label>
                    <input type="password" id="loginPassword" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                </div>
                <button type="submit" class="w-full bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700">登录</button>
            </form>
            <div class="mt-4 text-center">
                <button id="changePasswordBtn" class="text-blue-600 hover:text-blue-800 text-sm">修改密码</button>
            </div>
            <p id="loginError" class="text-red-500 text-center mt-4 hidden"></p>
        </div>
    </div>

    <!-- 修改密码弹窗 -->
    <div id="changePasswordModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <div class="flex justify-center mb-4">
                <div class="h-12 w-12 bg-blue-600 rounded-full flex items-center justify-center shadow-md">
                    <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSJ3aGl0ZSIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiIGNsYXNzPSJmZWF0aGVyIGZlYXRoZXItY2xpcGJvYXJkIj48cGF0aCBkPSJNMTYgNGgyYTIgMiAwIDAgMSAyIDJ2MTRhMiAyIDAgMCAxLTIgMkg2YTIgMiAwIDAgMS0yLTJWNmEyIDIgMCAwIDEgMi0yaDIiPjwvcGF0aD48cmVjdCB4PSI4IiB5PSIyIiB3aWR0aD0iOCIgaGVpZ2h0PSI0IiByeD0iMSIgcnk9IjEiPjwvcmVjdD48bGluZSB4MT0iOSIgeTE9IjkuMTA0IiB4Mj0iMTUiIHkyPSI5LjEwNCI+PC9saW5lPjxsaW5lIHgxPSI5IiB5MT0iMTIiIHgyPSIxMiIgeTI9IjEyIj48L2xpbmU+PGxpbmUgeDE9IjkiIHkxPSIxNSIgeDI9IjE1IiB5Mj0iMTUiPjwvbGluZT48L3N2Zz4="
                    alt="申请系统Logo" class="h-8 w-8">
                </div>
            </div>
            <h2 class="text-xl font-bold mb-4 text-center">修改密码</h2>
            <form id="changePasswordForm" class="space-y-4">
                <div>
                    <label class="block text-gray-700 mb-2">用户名</label>
                    <input type="text" id="cpUsername" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                    <p class="text-gray-500 text-xs mt-1">可以使用用户名或用户代码</p>
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">当前密码</label>
                    <input type="password" id="cpCurrentPassword" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">确认当前密码</label>
                    <input type="password" id="cpConfirmCurrentPassword" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                </div>
                <div>
                    <label class="block text-gray-700 mb-2">新密码</label>
                    <input type="password" id="cpNewPassword" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                </div>
                <div class="flex space-x-3">
                    <button type="submit" class="flex-1 bg-blue-600 text-white p-2 rounded-md hover:bg-blue-700">确认修改</button>
                    <button type="button" id="cancelChangePassword" class="flex-1 bg-gray-300 text-gray-800 p-2 rounded-md hover:bg-gray-400">取消</button>
                </div>
            </form>
            <p id="cpError" class="text-red-500 text-center mt-4 hidden"></p>
            <p id="cpSuccess" class="text-green-500 text-center mt-4 hidden"></p>
        </div>
    </div>

    <!-- 自定义确认对话框 -->
    <div id="customConfirmModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden">
        <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
            <h2 class="text-xl font-bold mb-4 text-center">请注意</h2>
            <p id="customConfirmMessage" class="text-center mb-6">您未填写申请/采购金额，点击<span class="text-blue-600 font-bold">确认</span>则直接提交申请，点击<span class="text-gray-800 font-bold">返回填写</span>完成金额填写</p>
            <div class="flex space-x-4 justify-center">
                <button id="customConfirmYes" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">确认</button>
                <button id="customConfirmNo" class="bg-gray-300 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-400">返回填写</button>
            </div>
        </div>
    </div>

    <!-- 主界面 -->
    <div id="mainApp" class="hidden">
        <!-- 顶部导航栏 - 简化版 -->
        <nav class="bg-blue-600 text-white p-4 shadow-lg md:hidden">
            <div class="container mx-auto flex justify-between items-center">
                <div class="flex items-center w-full justify-between">
                    <button id="menuToggleBtn" class="focus:outline-none mr-2">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16" />
                        </svg>
                    </button>
                    <!-- 移动端居中显示标题，点击可返回主页 -->
                    <div class="flex items-center flex-grow justify-center">
                        <div class="h-8 w-8 mr-2 bg-white rounded-full flex items-center justify-center shadow-sm">
                            <img src="data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMjU2M0VCIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIgY2xhc3M9ImZlYXRoZXIgZmVhdGhlci1jbGlwYm9hcmQiPjxwYXRoIGQ9Ik0xNiA0aDJhMiAyIDAgMCAxIDIgMnYxNGEyIDIgMCAwIDEtMiAySDZhMiAyIDAgMCAxLTItMlY2YTIgMiAwIDAgMSAyLTJoMiI+PC9wYXRoPjxyZWN0IHg9IjgiIHk9IjIiIHdpZHRoPSI4IiBoZWlnaHQ9IjQiIHJ4PSIxIiByeT0iMSI+PC9yZWN0PjxsaW5lIHgxPSI5IiB5MT0iOS4xMDQiIHgyPSIxNSIgeTI9IjkuMTA0Ij48L2xpbmU+PGxpbmUgeDE9IjkiIHkxPSIxMiIgeDI9IjEyIiB5Mj0iMTIiPjwvbGluZT48bGluZSB4MT0iOSIgeTE9IjE1IiB4Mj0iMTUiIHkyPSIxNSI+PC9saW5lPjwvc3ZnPg=="
                            alt="申请系统Logo" class="h-6 w-6">
                        </div>
                        <h1 class="text-2xl font-bold cursor-pointer" onclick="goToHomePage()">申请书管理系统</h1>
                    </div>

                    <!-- 移动端右侧显示用户信息和退出按钮 -->
                    <div class="flex items-center">
                        <button onclick="toggleUserInfo()" class="focus:outline-none">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                            </svg>
                        </button>
                    </div>
                </div>

                <!-- 移动端用户信息下拉菜单 -->
                <div id="userInfoDropdown" class="hidden absolute right-0 top-16 bg-blue-700 text-white p-3 rounded-bl-lg shadow-lg z-40 w-48">
                    <div id="mobileCurrentUserInfo" class="text-sm mb-2"></div>
                    <button onclick="logout()" class="w-full text-left py-2 px-3 rounded hover:bg-blue-600">退出登录</button>
                </div>
            </div>
        </nav>

        <!-- PC端布局容器 -->
        <div class="flex h-screen md:h-screen md:bg-gray-100">
            <!-- PC端侧边栏 -->
            <div id="pcSidebar" class="hidden md:flex md:flex-col md:w-64 bg-white border-r border-gray-200 shadow-sm md:rounded-lg md:border-0 md:fixed md:left-4 md:top-4 md:bottom-4 md:z-30">
                <!-- 侧边栏头部 -->
                <div class="p-6 border-b border-gray-100">
                    <div class="flex items-center">
                        <div class="h-16 w-16 mr-3 flex items-center justify-center cursor-pointer" onclick="goToHomePage()">
                            <img src="img/Makrite-logo.png" alt="Makrite Logo" class="h-12 w-auto object-contain">
                        </div>
                        <div>
                            <h2 class="text-lg font-semibold text-gray-900">申请书管理系统</h2>
                        </div>
                    </div>
                </div>

                <!-- 侧边栏导航菜单 -->
                <div class="flex-1 px-4 pb-4 space-y-1 overflow-hidden">
                    <button onclick="showSection('new')" class="pc-nav-btn w-full text-left py-3 px-4 rounded-lg hover:bg-gray-100 text-gray-700 hover:text-gray-900 transition-colors flex items-center font-medium" data-section="new">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                        </svg>
                        新建申请
                    </button>
                    <button onclick="showSection('history')" class="pc-nav-btn w-full text-left py-3 px-4 rounded-lg hover:bg-gray-100 text-gray-700 hover:text-gray-900 transition-colors flex items-center" data-section="history">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        申请记录
                    </button>
                    <button id="pcPendingApprovalBtn" onclick="showSection('pendingApproval')" class="pc-nav-btn w-full text-left py-3 px-4 rounded-lg hover:bg-gray-100 text-gray-700 hover:text-gray-900 hidden transition-colors flex items-center justify-between" data-section="pendingApproval">
                        <div class="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                            待审核
                        </div>
                        <span id="pcPendingCount" class="ml-2 px-2 py-1 bg-red-500 text-white text-xs rounded-full hidden">0</span>
                    </button>
                    <button id="pcApprovedBtn" onclick="showSection('approved')" class="pc-nav-btn w-full text-left py-3 px-4 rounded-lg hover:bg-gray-100 text-gray-700 hover:text-gray-900 hidden transition-colors flex items-center" data-section="approved">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        已审核
                    </button>

                    <button id="pcSystemSettingsBtn" onclick="showSection('systemSettings')" class="pc-nav-btn w-full text-left py-3 px-4 rounded-lg hover:bg-gray-100 text-gray-700 hover:text-gray-900 hidden transition-colors flex items-center" data-section="systemSettings">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z" />
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                        </svg>
                        系统设置
                    </button>
                    <button id="pcManageUsersBtn" onclick="showSection('manageUsers')" class="pc-nav-btn w-full text-left py-3 px-4 rounded-lg hover:bg-gray-100 text-gray-700 hover:text-gray-900 hidden transition-colors flex items-center" data-section="manageUsers">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                        </svg>
                        用户管理
                    </button>
                </div>

                <!-- 侧边栏底部用户信息 -->
                <div class="p-4 border-t border-gray-100">
                    <div class="flex items-center justify-between">
                        <div class="flex items-center flex-1 min-w-0">
                            <div class="h-10 w-10 bg-gray-300 rounded-full flex items-center justify-center mr-3 flex-shrink-0">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 text-gray-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                </svg>
                            </div>
                            <div class="min-w-0 flex-1">
                                <div id="pcCurrentUserInfo" class="text-sm font-medium text-gray-900 truncate"></div>
                            </div>
                        </div>
                        <button onclick="logout()" class="ml-3 bg-gray-100 hover:bg-gray-200 text-gray-700 p-2.5 rounded-lg transition-colors flex items-center justify-center flex-shrink-0" title="退出登录">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                            </svg>
                        </button>
                    </div>
                </div>
            </div>

        <!-- 侧边导航菜单 - 仅在移动端显示 -->
        <div id="sideMenu" class="fixed inset-y-0 left-0 transform -translate-x-full bg-blue-700 bg-opacity-80 text-white w-64 z-30 transition-transform duration-300 ease-in-out shadow-xl overflow-y-auto backdrop-blur-md md:hidden">
            <div class="p-3 border-b border-blue-800 border-opacity-50">
                <div class="flex items-center justify-between">
                    <h2 class="text-xl font-bold">功能菜单</h2>
                    <button id="closeMenuBtn" class="focus:outline-none text-white hover:text-gray-200 transition-colors">
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
                        </svg>
                    </button>
                </div>
            </div>
            <div class="p-3 space-y-2">
                <button onclick="showSection('new'); toggleMenuMobile();" class="side-nav-btn w-full text-left py-2 px-3 rounded hover:bg-blue-600 hover:bg-opacity-70 active-indicator" data-section="new">
                    <span class="inline-block w-5 mr-1">📝</span>新建申请
                </button>
                <button onclick="showSection('history'); toggleMenuMobile();" class="side-nav-btn w-full text-left py-2 px-3 rounded hover:bg-blue-600 hover:bg-opacity-70" data-section="history">
                    <span class="inline-block w-5 mr-1">📋</span>申请记录
                </button>
                <button id="sidePendingApprovalBtn" onclick="showSection('pendingApproval'); toggleMenuMobile();" class="side-nav-btn w-full text-left py-2 px-3 rounded hover:bg-blue-600 hover:bg-opacity-70 hidden" data-section="pendingApproval">
                    <span class="inline-block w-5 mr-1">⏳</span>待审核
                    <span id="sidePendingCount" class="ml-1 px-1.5 py-0.5 bg-red-500 text-white text-xs rounded-full hidden">0</span>
                </button>
                <button id="sideApprovedBtn" onclick="showSection('approved'); toggleMenuMobile();" class="side-nav-btn w-full text-left py-2 px-3 rounded hover:bg-blue-600 hover:bg-opacity-70 hidden" data-section="approved">
                    <span class="inline-block w-5 mr-1">✅</span>已审核
                </button>
                <button id="sideSystemSettingsBtn" onclick="showSection('systemSettings'); toggleMenuMobile();" class="side-nav-btn w-full text-left py-2 px-3 rounded hover:bg-blue-600 hover:bg-opacity-70 hidden" data-section="systemSettings">
                    <span class="inline-block w-5 mr-1">⚙️</span>系统设置
                </button>
                <button id="sideManageUsersBtn" onclick="showSection('manageUsers'); toggleMenuMobile();" class="side-nav-btn w-full text-left py-2 px-3 rounded hover:bg-blue-600 hover:bg-opacity-70 hidden" data-section="manageUsers">
                    <span class="inline-block w-5 mr-1">👥</span>用户管理
                </button>
                <!-- 刷新数据按钮已删除 -->
            </div>
        </div>

            <!-- PC端主内容区域 -->
            <div class="flex-1 md:overflow-y-auto bg-white shadow-lg md:rounded-lg md:ml-72 md:mr-4 md:mt-4 md:mb-4">
                <main class="w-full mt-8 px-4 md:px-6 transition-all duration-300 md:mt-0 md:p-6">
            <!-- 新建申请表单 -->
            <section id="newSection" class="bg-white p-6 rounded-lg shadow-md space-y-6">
                <h2 class="text-2xl font-semibold text-gray-800">新建申请书</h2>
                <form id="applicationForm" class="space-y-4">
                    <div class="flex space-x-4">
                        <div class="w-1/2">
                            <label class="block text-gray-700 mb-2">申请人姓名</label>
                            <input type="text" id="applicant" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 bg-gray-100 cursor-not-allowed" readonly>
                        </div>
                        <div class="w-1/2">
                            <label class="block text-gray-700 mb-2">申请部门</label>
                            <input type="text" id="department" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500 bg-gray-100 cursor-not-allowed" readonly>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="w-1/2">
                            <label class="block text-gray-700 mb-2">申请日期</label>
                            <input type="date" id="applyDate" required class="w-full p-2 border rounded-md">
                        </div>
                        <div class="w-1/2"></div>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">申请内容</label>
                        <textarea id="content" rows="5" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"></textarea>
                    </div>
                    <div class="flex space-x-4">
                        <div class="w-1/2">
                            <label class="block text-gray-700 mb-2">申请/采购金额</label>
                            <div class="flex space-x-2">
                                <select id="currency" class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500 w-24">
                                    <option value="CNY">人民币</option>
                                    <option value="USD">美元</option>
                                </select>
                                <input type="number" id="amount" min="0" step="0.01" class="flex-1 p-2 border rounded-md focus:ring-2 focus:ring-blue-500" placeholder="请输入金额">
                            </div>
                        </div>
                        <div class="w-1/2">
                            <label class="block text-gray-700 mb-2">紧急程度</label>
                            <select id="priority" class="w-full p-2 border rounded-md">
                                <option value="normal">普通</option>
                                <option value="medium">中等</option>
                                <option value="high">紧急</option>
                            </select>
                        </div>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">选择审批厂长</label>
                        <div id="directorsList" class="max-h-40 overflow-y-auto border rounded-md p-2">
                            <p class="text-gray-500 text-center">加载中...</p>
                        </div>
                        <p class="text-xs text-gray-500 mt-1">如果您的申请不需要经过厂长审批，请直接选择总监进行审批</p>
                    </div>
                    <div>
                        <label class="block text-gray-700 mb-2">附件上传</label>
                        <div class="border-2 border-dashed border-gray-300 p-4 rounded-md">
                            <input type="file" id="attachments" name="attachments" multiple class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                            <div id="fileInputArea" class="text-center cursor-pointer" onclick="document.getElementById('attachments').click()">
                                <p class="text-gray-500">点击选择文件或拖拽文件到此区域</p>
                                <p class="text-sm text-gray-400">支持格式：PDF/DOC/图片（最大15MB）</p>
                            </div>
                            <div id="fileList" class="mt-4 space-y-2"></div>
                        </div>
                    </div>
                    <div class="flex space-x-4">
                        <button type="submit" id="submitBtn" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700 disabled:bg-gray-400 disabled:cursor-not-allowed transition-colors duration-200">
                            <span id="submitBtnText">提交申请</span>
                            <svg id="submitBtnSpinner" class="hidden animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                            </svg>
                        </button>
                        <button type="button" onclick="previewApplicationTemplate()" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700">预览申请书</button>
                    </div>
                </form>
            </section>

            <!-- 申请记录 -->
            <section id="historySection" class="hidden bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">申请记录</h2>
                <div class="mb-4 flex flex-wrap justify-between items-center gap-y-2">
                    <div class="flex flex-wrap space-x-4 gap-y-2">
                        <select id="timeRange" class="p-2 border rounded-md" onchange="updateHistoryList()">
                            <option value="all">全部</option>
                            <option value="week">本周</option>
                            <option value="month">本月</option>
                            <option value="year">本年</option>
                        </select>
                        <select id="searchField" class="p-2 border rounded-md">
                            <option value="applicant">申请人</option>
                            <option value="content" selected>申请内容</option>
                        </select>
                        <div class="relative w-64">
                            <input type="text" id="searchInput" placeholder="请输入搜索内容..." class="p-2 pr-8 border rounded-md w-full">
                            <button type="button" id="clearSearchInput" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                                </svg>
                            </button>
                        </div>
                        <button onclick="searchApplications()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">搜索</button>
                    </div>
                    <div class="flex items-center space-x-2">
                        <div id="exportDateRangeContainer" class="hidden">
                            <div class="flex items-center space-x-2">
                                <input type="date" id="exportStartDate" class="p-2 border rounded-md" title="开始日期">
                                <span class="text-gray-500">至</span>
                                <input type="date" id="exportEndDate" class="p-2 border rounded-md" title="结束日期">
                            </div>
                        </div>
                        <div class="flex items-center">
                            <button id="toggleExportDateRange" class="bg-blue-600 text-white px-3 py-2 rounded-md hover:bg-blue-700 mr-2">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd" />
                                </svg>
                            </button>
                            <button onclick="exportApplicationsToExcel()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700 flex items-center">
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                    <path fill-rule="evenodd" d="M3 17a1 1 0 011-1h12a1 1 0 110 2H4a1 1 0 01-1-1zm3.293-7.707a1 1 0 011.414 0L9 10.586V3a1 1 0 112 0v7.586l1.293-1.293a1 1 0 111.414 1.414l-3 3a1 1 0 01-1.414 0l-3-3a1 1 0 010-1.414z" clip-rule="evenodd" />
                                </svg>
                                导出Excel
                            </button>
                        </div>
                    </div>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-left">申请编号</th>
                                <th class="px-6 py-3 text-left">申请人</th>
                                <th class="px-6 py-3 text-left">申请部门</th>
                                <th class="px-6 py-3 text-left">申请日期</th>
                                <th class="px-6 py-3 text-left">紧急程度</th>
                                <th class="px-6 py-3 text-left">申请内容</th>
                                <th class="px-6 py-3 text-right">申请金额</th>
                                <th class="px-6 py-3 text-left">状态</th>
                                <th class="px-6 py-3 text-left">操作</th>
                            </tr>
                        </thead>
                        <tbody id="applicationsList" class="divide-y divide-gray-200"></tbody>
                        <tfoot>
                            <tr class="bg-gray-100">
                                <td colspan="6" class="px-6 py-3 text-right font-bold">合计：</td>
                                <td class="px-6 py-3 text-right font-bold text-blue-600" id="currentPageTotalAmount">¥0.00</td>
                                <td colspan="2"></td>
                            </tr>
                        </tfoot>
                    </table>
                </div>
                <!-- 分页控件 -->
                <div id="historyPagination" class="mt-4 flex justify-between items-center">
                    <div class="text-sm text-gray-500">
                        共 <span id="historyTotalItems">0</span> 条记录，每页 10 条
                    </div>
                    <div class="flex space-x-2">
                        <button id="historyPrevPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">上一页</button>
                        <div id="historyPageNumbers" class="flex space-x-1"></div>
                        <button id="historyNextPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">下一页</button>
                    </div>
                </div>
            </section>

            <!-- 待审核页面 -->
            <section id="pendingApprovalSection" class="hidden bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">待审核申请</h2>

                <!-- 移动端卡片式显示 -->
                <div id="pendingApprovalCards" class="hidden">
                    <div class="mb-4 flex justify-between items-center">
                        <div class="text-sm text-gray-500">
                            待审核: <span id="currentCardIndex">1</span>/<span id="totalCards">0</span>
                        </div>
                        <div class="flex space-x-2">
                            <button id="prevCardBtn" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">上一个</button>
                            <button id="nextCardBtn" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">下一个</button>
                        </div>
                    </div>
                    <div id="pendingCardContainer" class="bg-white rounded-lg shadow-md p-4 border border-gray-200">
                        <!-- 卡片内容将通过JavaScript动态生成 -->
                    </div>
                </div>

                <!-- PC端表格显示 -->
                <div id="pendingApprovalTable" class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-left">申请编号</th>
                                <th class="px-6 py-3 text-left">申请人</th>
                                <th class="px-6 py-3 text-left">申请部门</th>
                                <th class="px-6 py-3 text-left">申请日期</th>
                                <th class="px-6 py-3 text-left">紧急程度</th>
                                <th class="px-6 py-3 text-left">申请内容</th>
                                <th class="px-6 py-3 text-left">状态</th>
                                <th class="px-6 py-3 text-left">操作</th>
                            </tr>
                        </thead>
                        <tbody id="pendingApprovalList" class="divide-y divide-gray-200"></tbody>
                    </table>
                </div>
                <!-- 添加待审核分页控件 -->
                <div class="mt-4 flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        共 <span id="pendingTotalItems">0</span> 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="pendingPrevPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">上一页</button>
                        <div id="pendingPageNumbers" class="flex space-x-1"></div>
                        <button id="pendingNextPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">下一页</button>
                    </div>
                </div>
            </section>

            <!-- 已审核页面 -->
            <section id="approvedSection" class="hidden bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">已审核申请</h2>
                <div class="mb-4 flex flex-wrap space-x-4 gap-y-2">
                    <select id="approvedTimeRange" class="p-2 border rounded-md" onchange="updateApprovedList()">
                        <option value="all">全部</option>
                        <option value="week">本周</option>
                        <option value="month">本月</option>
                        <option value="year">本年</option>
                    </select>
                    <select id="approvedStatusFilter" class="p-2 border rounded-md" onchange="updateApprovedList()">
                        <option value="all">全部状态</option>
                        <option value="approved">通过申请</option>
                        <option value="rejected">拒绝申请</option>
                    </select>
                    <select id="approvedSearchField" class="p-2 border rounded-md">
                        <option value="applicant">申请人</option>
                        <option value="content" selected>申请内容</option>
                    </select>
                    <div class="relative w-64">
                        <input type="text" id="approvedSearchInput" placeholder="请输入搜索内容..." class="p-2 pr-8 border rounded-md w-full">
                        <button type="button" id="clearApprovedSearchInput" class="absolute right-2 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 hidden">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd" />
                            </svg>
                        </button>
                    </div>
                    <button onclick="searchApprovedApplications()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">搜索</button>
                </div>
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-left">申请编号</th>
                                <th class="px-6 py-3 text-left">申请人</th>
                                <th class="px-6 py-3 text-left">申请部门</th>
                                <th class="px-6 py-3 text-left">申请日期</th>
                                <th class="px-6 py-3 text-left">紧急程度</th>
                                <th class="px-6 py-3 text-left">申请内容</th>
                                <th class="px-6 py-3 text-left">状态</th>
                                <th class="px-6 py-3 text-left">审核结果</th>
                                <th class="px-6 py-3 text-left">操作</th>
                            </tr>
                        </thead>
                        <tbody id="approvedList" class="divide-y divide-gray-200"></tbody>
                    </table>
                </div>
                <!-- 添加已审核分页控件 -->
                <div class="mt-4 flex items-center justify-between">
                    <div class="text-sm text-gray-600">
                        共 <span id="approvedTotalItems">0</span> 条记录
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="approvedPrevPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">上一页</button>
                        <div id="approvedPageNumbers" class="flex space-x-1"></div>
                        <button id="approvedNextPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed">下一页</button>
                    </div>
                </div>
            </section>

            <!-- 系统设置（仅 admin 可见） -->
            <section id="systemSettingsSection" class="hidden bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">系统设置</h2>

                <!-- 邮件提醒设置 -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-700 mb-4">邮件提醒设置</h3>



                    <!-- 提醒策略设置 -->
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="space-y-4">
                            <!-- 按优先级设置 -->
                            <div>
                                <h5 class="text-sm font-medium text-gray-600 mb-2">按优先级设置</h5>
                                <div class="bg-blue-50 p-3 rounded-md mb-4 text-sm text-blue-800">
                                    <p class="font-medium mb-2">⏰ 时间间隔说明：</p>
                                    <ul class="space-y-1 text-xs">
                                        <li><strong>初始延迟</strong>：每个审批层级开始后多长时间发送第一次提醒邮件</li>
                                        <li><strong>正常间隔</strong>：当前层级等待0-24小时内的提醒间隔时间</li>
                                        <li><strong>中期间隔</strong>：当前层级等待24-48小时内的提醒间隔时间</li>
                                        <li><strong>紧急间隔</strong>：当前层级等待48小时以上的提醒间隔时间</li>
                                    </ul>
                                    <div class="mt-2 p-2 bg-green-50 border border-green-200 rounded-md">
                                        <p class="text-xs text-green-800">
                                            <strong>💡 重要提示：</strong>系统采用分层级独立计时，每个审批层级（厂长→总监→经理→CEO）都会重新开始计时，确保审批人只在自己的处理时间超时后才收到提醒。
                                        </p>
                                    </div>
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="border rounded-lg p-3">
                                        <h6 class="font-medium text-red-600 mb-2">紧急申请</h6>
                                        <div class="space-y-2">
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">初始延迟:</label>
                                                <input type="number" id="highPriorityDelay" min="1" max="12" value="4" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">正常间隔:</label>
                                                <input type="number" id="highPriorityNormal" min="1" max="12" value="4" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">中期间隔:</label>
                                                <input type="number" id="highPriorityMedium" min="1" max="6" value="2" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">紧急间隔:</label>
                                                <input type="number" id="highPriorityUrgent" min="1" max="3" value="1" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="border rounded-lg p-3">
                                        <h6 class="font-medium text-yellow-600 mb-2">中等申请</h6>
                                        <div class="space-y-2">
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">初始延迟:</label>
                                                <input type="number" id="mediumPriorityDelay" min="1" max="24" value="8" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">正常间隔:</label>
                                                <input type="number" id="mediumPriorityNormal" min="1" max="12" value="8" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">中期间隔:</label>
                                                <input type="number" id="mediumPriorityMedium" min="1" max="8" value="4" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">紧急间隔:</label>
                                                <input type="number" id="mediumPriorityUrgent" min="1" max="4" value="2" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="border rounded-lg p-3">
                                        <h6 class="font-medium text-green-600 mb-2">普通申请</h6>
                                        <div class="space-y-2">
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">初始延迟:</label>
                                                <input type="number" id="lowPriorityDelay" min="1" max="24" value="12" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">正常间隔:</label>
                                                <input type="number" id="lowPriorityNormal" min="1" max="24" value="12" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">中期间隔:</label>
                                                <input type="number" id="lowPriorityMedium" min="1" max="12" value="6" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                            <div class="flex items-center space-x-2">
                                                <label class="text-xs text-gray-600">紧急间隔:</label>
                                                <input type="number" id="lowPriorityUrgent" min="1" max="6" value="3" class="w-16 p-1 border rounded text-xs">
                                                <span class="text-xs text-gray-600">小时</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>


                        </div>

                        <!-- 时间控制设置 -->
                        <div class="mt-6">
                            <h5 class="text-sm font-medium text-gray-600 mb-3">⏰ 邮件提醒时间控制</h5>
                            <div class="bg-yellow-50 p-3 rounded-md mb-4 text-sm text-yellow-800">
                                <p class="font-medium mb-2">📅 时间控制说明：</p>
                                <ul class="space-y-1 text-xs">
                                    <li><strong>工作日时间</strong>：可以自定义哪些天为工作日，以及工作时间范围</li>
                                    <li><strong>自定义日期</strong>：可以设置特定的日期不发送提醒（如节假日）</li>
                                </ul>
                            </div>

                            <div class="space-y-6">
                                <!-- 工作日和工作时间设置 -->
                                <div class="bg-gradient-to-r from-blue-50 to-indigo-50 border border-blue-200 rounded-lg p-4">
                                    <div class="flex items-center space-x-3 mb-4">
                                        <input type="checkbox" id="enableWorkingDays" class="rounded text-blue-600 focus:ring-blue-500">
                                        <label for="enableWorkingDays" class="text-sm font-semibold text-gray-800 flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                            </svg>
                                            启用工作日和工作时间限制
                                        </label>
                                    </div>
                                    <div id="workingDaysSettings" class="ml-7 hidden">
                                        <!-- 工作日选择 -->
                                        <div class="mb-5">
                                            <label class="block text-sm font-medium text-gray-700 mb-3">选择工作日:</label>
                                            <div class="bg-white rounded-lg p-3 border border-gray-200">
                                                <div class="grid grid-cols-7 gap-3">
                                                    <label class="flex flex-col items-center space-y-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors">
                                                        <input type="checkbox" id="workDay1" value="1" class="rounded text-blue-600 focus:ring-blue-500">
                                                        <span class="text-xs font-medium text-gray-700">周一</span>
                                                    </label>
                                                    <label class="flex flex-col items-center space-y-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors">
                                                        <input type="checkbox" id="workDay2" value="2" class="rounded text-blue-600 focus:ring-blue-500">
                                                        <span class="text-xs font-medium text-gray-700">周二</span>
                                                    </label>
                                                    <label class="flex flex-col items-center space-y-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors">
                                                        <input type="checkbox" id="workDay3" value="3" class="rounded text-blue-600 focus:ring-blue-500">
                                                        <span class="text-xs font-medium text-gray-700">周三</span>
                                                    </label>
                                                    <label class="flex flex-col items-center space-y-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors">
                                                        <input type="checkbox" id="workDay4" value="4" class="rounded text-blue-600 focus:ring-blue-500">
                                                        <span class="text-xs font-medium text-gray-700">周四</span>
                                                    </label>
                                                    <label class="flex flex-col items-center space-y-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors">
                                                        <input type="checkbox" id="workDay5" value="5" class="rounded text-blue-600 focus:ring-blue-500">
                                                        <span class="text-xs font-medium text-gray-700">周五</span>
                                                    </label>
                                                    <label class="flex flex-col items-center space-y-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors">
                                                        <input type="checkbox" id="workDay6" value="6" class="rounded text-blue-600 focus:ring-blue-500">
                                                        <span class="text-xs font-medium text-gray-700">周六</span>
                                                    </label>
                                                    <label class="flex flex-col items-center space-y-2 p-2 rounded-md hover:bg-gray-50 cursor-pointer transition-colors">
                                                        <input type="checkbox" id="workDay7" value="7" class="rounded text-blue-600 focus:ring-blue-500">
                                                        <span class="text-xs font-medium text-gray-700">周日</span>
                                                    </label>
                                                </div>
                                            </div>
                                        </div>
                                        <!-- 工作时间设置 -->
                                        <div class="bg-white rounded-lg p-3 border border-gray-200">
                                            <label class="block text-sm font-medium text-gray-700 mb-3">工作时间范围:</label>
                                            <div class="flex items-center space-x-4">
                                                <div class="flex-1">
                                                    <label class="block text-xs text-gray-600 mb-2">开始时间</label>
                                                    <input type="time" id="workingStartTime" value="09:00" class="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                </div>
                                                <div class="flex items-center pt-6">
                                                    <span class="text-gray-400 text-sm">至</span>
                                                </div>
                                                <div class="flex-1">
                                                    <label class="block text-xs text-gray-600 mb-2">结束时间</label>
                                                    <input type="time" id="workingEndTime" value="18:00" class="w-full p-2 border border-gray-300 rounded-md text-sm focus:ring-2 focus:ring-blue-500 focus:border-blue-500">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <!-- 自定义跳过日期设置 -->
                                <div class="bg-gradient-to-r from-green-50 to-emerald-50 border border-green-200 rounded-lg p-4">
                                    <div class="flex items-center space-x-3 mb-4">
                                        <input type="checkbox" id="enableCustomDates" class="rounded text-green-600 focus:ring-green-500">
                                        <label for="enableCustomDates" class="text-sm font-semibold text-gray-800 flex items-center">
                                            <svg class="w-4 h-4 mr-2 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                                            </svg>
                                            启用自定义跳过日期
                                        </label>
                                    </div>
                                    <div id="customDatesSettings" class="ml-7 hidden">
                                        <div class="mb-5">
                                            <label class="block text-sm font-medium text-gray-700 mb-3">添加跳过日期:</label>
                                            <div class="bg-white rounded-lg p-4 border border-gray-200 space-y-3">
                                                <!-- 单个日期添加 -->
                                                <div class="flex items-center space-x-3">
                                                    <div class="flex-shrink-0">
                                                        <label class="block text-xs text-gray-600 mb-1">单个日期</label>
                                                        <input type="date" id="newSkipDate" class="p-2 border border-gray-300 rounded-md text-sm w-40 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                                    </div>
                                                    <button onclick="addSkipDate()" class="mt-5 bg-blue-600 text-white px-4 py-2 rounded-md text-sm hover:bg-blue-700 transition-colors">
                                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                                        </svg>
                                                        添加
                                                    </button>
                                                </div>

                                                <!-- 分隔线 -->
                                                <div class="flex items-center">
                                                    <div class="flex-1 border-t border-gray-200"></div>
                                                    <span class="px-3 text-xs text-gray-500 bg-white">或</span>
                                                    <div class="flex-1 border-t border-gray-200"></div>
                                                </div>

                                                <!-- 日期范围添加 -->
                                                <div class="flex items-center space-x-3">
                                                    <div class="flex-shrink-0">
                                                        <label class="block text-xs text-gray-600 mb-1">开始日期</label>
                                                        <input type="date" id="rangeStartDate" class="p-2 border border-gray-300 rounded-md text-sm w-40 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                                    </div>
                                                    <div class="flex items-center pt-5">
                                                        <span class="text-gray-400 text-sm">至</span>
                                                    </div>
                                                    <div class="flex-shrink-0">
                                                        <label class="block text-xs text-gray-600 mb-1">结束日期</label>
                                                        <input type="date" id="rangeEndDate" class="p-2 border border-gray-300 rounded-md text-sm w-40 focus:ring-2 focus:ring-green-500 focus:border-green-500">
                                                    </div>
                                                    <button onclick="addDateRange()" class="mt-5 bg-green-600 text-white px-4 py-2 rounded-md text-sm hover:bg-green-700 transition-colors">
                                                        <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                                                        </svg>
                                                        批量添加
                                                    </button>
                                                </div>

                                                <div class="bg-blue-50 border border-blue-200 rounded-md p-3">
                                                    <div class="flex items-start">
                                                        <svg class="w-4 h-4 text-blue-600 mt-0.5 mr-2 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                                        </svg>
                                                        <p class="text-xs text-blue-700">
                                                            <strong>提示：</strong>可以单个添加特定日期，也可以选择日期范围进行批量添加（如节假日期间）
                                                        </p>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        <div>
                                            <label class="block text-sm font-medium text-gray-700 mb-3">已设置的跳过日期:</label>
                                            <div id="skipDatesList" class="bg-white rounded-lg border border-gray-200 p-3 space-y-2 max-h-48 overflow-y-auto">
                                                <!-- 跳过日期列表将在这里显示 -->
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="mt-4 flex space-x-4">
                            <button onclick="saveReminderStrategies()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                保存提醒策略
                            </button>
                            <button onclick="triggerReminderCheck()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                立即检查提醒
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 提醒统计信息 -->
                <div class="mb-8">
                    <h3 class="text-lg font-medium text-gray-700 mb-4">提醒统计</h3>
                    <div class="bg-gray-50 p-4 rounded-lg">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                            <div class="text-center">
                                <div class="text-2xl font-bold text-blue-600" id="totalReminders">-</div>
                                <div class="text-sm text-gray-600">今日发送提醒</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-yellow-600" id="pendingApplications">-</div>
                                <div class="text-sm text-gray-600">待审批申请</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-red-600" id="urgentApplications">-</div>
                                <div class="text-sm text-gray-600">紧急申请</div>
                            </div>
                            <div class="text-center">
                                <div class="text-2xl font-bold text-green-600" id="avgResponseTime">-</div>
                                <div class="text-sm text-gray-600">平均响应时间(小时)</div>
                            </div>
                        </div>
                        <button onclick="loadReminderStats()" class="mt-4 bg-gray-600 text-white px-4 py-2 rounded-md hover:bg-gray-700">
                            刷新统计
                        </button>
                    </div>
                </div>
            </section>



            <!-- 用户管理（仅 admin 可见） -->
            <section id="manageUsersSection" class="hidden bg-white p-6 rounded-lg shadow-md">
                <h2 class="text-2xl font-semibold text-gray-800 mb-6">用户管理</h2>

                <div class="overflow-x-auto">
                    <div id="deleteModeAlert" class="hidden bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4">
                        <p class="font-bold">删除模式已启用</p>
                        <p>点击用户行右侧的"×"按钮可以删除对应用户。点击"取消删除"按钮可退出删除模式。</p>
                    </div>

                    <!-- 用户搜索框和操作按钮 -->
                    <div class="flex flex-wrap justify-between items-center mb-4 gap-2">
                        <div class="flex items-center space-x-2">
                            <input type="text" id="userSearchInput" placeholder="搜索用户名或用户代码..." class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500 w-64">
                            <button onclick="searchUsers()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">搜索</button>
                            <button onclick="resetUserSearch()" class="bg-gray-500 text-white px-4 py-2 rounded-md hover:bg-gray-600">重置</button>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="showAddUserModal()" class="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                                </svg>
                                添加新用户
                            </button>
                            <button id="toggleDeleteModeBtn" onclick="toggleDeleteMode()" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700">
                                <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                                </svg>
                                删除用户
                            </button>
                            <button onclick="exportUsers()" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">导出用户CSV</button>
                            <label for="importUsersInput" class="bg-purple-600 text-white px-4 py-2 rounded-md hover:bg-purple-700 cursor-pointer inline-block">
                                导入用户CSV
                                <input type="file" id="importUsersInput" accept=".csv" class="hidden" onchange="importUsers(event)">
                            </label>
                        </div>
                    </div>

                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-6 py-3 text-left">用户名</th>
                                <th class="px-6 py-3 text-left">用户代码</th>
                                <th class="px-6 py-3 text-left">角色</th>
                                <th class="px-6 py-3 text-left">部门</th>
                                <th class="px-6 py-3 text-left">电子签名</th>
                                <th class="px-6 py-3 text-left">操作</th>
                            </tr>
                        </thead>
                        <tbody id="usersList" class="divide-y divide-gray-200"></tbody>
                    </table>
                    <!-- 分页控件 -->
                    <div id="usersPagination" class="flex justify-center items-center mt-4 space-x-2">
                        <button id="prevPageBtn" class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed">上一页</button>
                        <span id="paginationInfo" class="text-gray-600">第 <span id="currentPage">1</span> 页，共 <span id="totalPages">1</span> 页</span>
                        <button id="nextPageBtn" class="px-3 py-1 bg-gray-200 rounded hover:bg-gray-300 disabled:opacity-50 disabled:cursor-not-allowed">下一页</button>
                    </div>
                </div>
            </section>

            <!-- 编辑申请模态框 -->
            <div id="editModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 overflow-y-auto">
                <div class="relative mx-auto my-10 bg-white rounded-lg p-6 w-full max-w-2xl">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-2xl font-bold">编辑申请</h2>
                        <button onclick="closeEditModal()" class="text-gray-500 hover:text-gray-700">✕</button>
                    </div>
                    <div class="max-h-[70vh] overflow-y-auto pr-2">
                    <form id="editForm" class="space-y-4">
                        <input type="hidden" id="editId">
                        <div>
                            <label class="block text-gray-700 mb-2">申请人姓名</label>
                            <input type="text" id="editApplicant" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">申请部门</label>
                            <select id="editApplicationDepartment" class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                                <option value="生产部">生产部</option>
                                <option value="采购部">采购部</option>
                                <option value="工程部">工程部</option>
                                <option value="机电部">机电部</option>
                                <option value="研发部">研发部</option>
                                <option value="品管部">品管部</option>
                                <option value="财务部">财务部</option>
                                <option value="人事部">人事部</option>
                                <option value="业务部">业务部</option>
                                <option value="管理部">管理部</option>
                                <option value="后勤部">后勤部</option>
                                <option value="仓储部">仓储部</option>
                                <option value="经理室">经理室</option>
                            </select>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">申请日期</label>
                            <input type="date" id="editDate" required class="w-full p-2 border rounded-md">
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">申请内容</label>
                            <textarea id="editContent" rows="5" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"></textarea>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">申请/采购金额</label>
                            <div class="flex space-x-2">
                                <select id="editCurrency" class="p-2 border rounded-md focus:ring-2 focus:ring-blue-500 w-24">
                                    <option value="CNY">人民币</option>
                                    <option value="USD">美元</option>
                                </select>
                                <input type="number" id="editAmount" min="0" step="0.01" class="flex-1 p-2 border rounded-md focus:ring-2 focus:ring-blue-500" placeholder="请输入金额">
                            </div>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">紧急程度</label>
                            <select id="editPriority" class="w-full p-2 border rounded-md">
                                <option value="normal">普通</option>
                                <option value="medium">中等</option>
                                <option value="high">紧急</option>
                            </select>
                        </div>
                            <div>
                                <label class="block text-gray-700 mb-2">选择审批厂长</label>
                                <div id="editDirectorsList" class="max-h-40 overflow-y-auto border rounded-md p-2">
                                    <p class="text-gray-500 text-center">加载中...</p>
                                </div>
                        </div>
                        <div>
                            <label class="block text-gray-700 mb-2">附件上传</label>
                            <div class="border-2 border-dashed border-gray-300 p-4 rounded-md">
                                <input type="file" id="editAttachments" name="attachments" multiple class="hidden" accept=".pdf,.doc,.docx,.jpg,.jpeg,.png">
                                <div id="editFileInputArea" class="text-center cursor-pointer" onclick="document.getElementById('editAttachments').click()">
                                    <p class="text-gray-500">点击选择文件或拖拽文件到此区域</p>
                                    <p class="text-sm text-gray-400">支持格式：PDF/DOC/图片（最大15MB）</p>
                                </div>
                                <div id="editFileList" class="mt-4 space-y-2"></div>
                                <div class="mt-2 p-2 bg-blue-50 text-blue-700 rounded-md text-sm">
                                    <p class="font-medium">提示：</p>
                                    <p>1. 如果申请处于待厂长审核状态，您可以随时修改申请内容和附件</p>
                                    <p>2. 如果申请已进入审批流程但一开始没有添加附件，您仍可以添加附件</p>
                                    <p>3. 一旦申请最终审核完成（已通过/已拒绝），将无法再修改</p>
                                </div>
                            </div>
                        </div>
                        <div class="flex space-x-4">
                            <button type="submit" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">保存修改</button>
                            <button type="button" onclick="closeEditModal()" class="bg-gray-600 text-white px-6 py-2 rounded-md hover:bg-gray-700">取消</button>
                        </div>
                    </form>
                    </div>
                </div>
            </div>

            <!-- 详情模态框 -->
            <div id="detailModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="relative mx-auto my-4 bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col">
                    <!-- 标题栏 -->
                    <div class="flex justify-between items-center p-4 border-b flex-shrink-0">
                        <h2 class="text-2xl font-bold">申请详情</h2>
                        <button onclick="closeDetail()" class="text-gray-500 hover:text-gray-700 focus:outline-none">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- 标签页导航 -->
                    <div class="border-b flex-shrink-0">
                        <div class="flex">
                            <button id="tab-basic" class="px-6 py-3 border-b-2 border-blue-500 font-medium text-blue-600">基本信息</button>
                            <button id="tab-approvals" class="px-6 py-3 text-gray-500 hover:text-gray-700">审批记录</button>
                            <button id="tab-attachments" class="px-6 py-3 text-gray-500 hover:text-gray-700">附件管理</button>
                        </div>
                    </div>

                    <!-- 内容区域 - 使用flex-grow让内容区域自动填充剩余空间 -->
                    <div class="flex-grow overflow-y-auto p-4">
                        <!-- 基本信息标签页 -->
                        <div id="content-basic" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                <!-- 左侧基本信息 -->
                                <div class="space-y-4">
                                    <div class="bg-gray-50 p-4 rounded-lg">
                                        <h3 class="font-semibold text-lg mb-3 text-gray-800">申请信息</h3>
                                        <div class="space-y-2">
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">申请编号</span>
                                                <span id="detail-code" class="font-medium"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">申请人</span>
                                                <span id="detail-applicant" class="font-medium"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">申请部门</span>
                                                <span id="detail-department" class="font-medium"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">申请日期</span>
                                                <span id="detail-date" class="font-medium"></span>
                                            </div>
                                            <div class="flex justify-between">
                                                <span class="text-gray-600">当前状态</span>
                                                <span id="detail-status" class="font-medium"></span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 申请金额卡片 -->
                                    <div class="bg-blue-50 p-4 rounded-lg">
                                        <h3 class="font-semibold text-lg mb-2 text-blue-800">申请金额</h3>
                                        <div class="text-center">
                                            <span id="detail-amount" class="text-2xl font-bold text-blue-700"></span>
                                        </div>
                                    </div>
                                </div>

                                <!-- 右侧申请内容 -->
                                <div class="bg-gray-50 p-4 rounded-lg">
                                    <h3 class="font-semibold text-lg mb-3 text-gray-800">申请内容</h3>
                                    <div id="detail-content" class="whitespace-pre-wrap bg-white p-3 rounded border overflow-y-auto" style="max-height: 300px;"></div>
                                </div>
                            </div>

                            <!-- 操作按钮区域 -->
                            <div class="flex flex-wrap gap-3 justify-center mt-4">
                                <button id="detail-view-template" class="bg-green-600 text-white px-4 py-2 rounded-md hover:bg-green-700">
                                    查看申请书
                                </button>
                                <button id="detail-withdraw" class="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700 hidden">
                                    撤回审批
                                </button>
                            </div>
                        </div>

                        <!-- 审批记录标签页 -->
                        <div id="content-approvals" class="hidden space-y-4">
                            <!-- 厂长审批记录 -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-lg mb-3 text-gray-800">厂长审批</h3>
                                <div id="detail-directors-approvals" class="space-y-3 max-h-[200px] overflow-y-auto pr-2"></div>
                            </div>

                            <!-- 总监审批记录 -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-lg mb-3 text-gray-800">总监审批</h3>
                                <div id="detail-chief-approval" class="space-y-3"></div>
                            </div>

                            <!-- 经理审批记录 -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-lg mb-3 text-gray-800">经理审批</h3>
                                <div id="detail-managers-approvals" class="space-y-3 max-h-[200px] overflow-y-auto pr-2"></div>
                            </div>

                            <!-- CEO审批记录 -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-lg mb-3 text-gray-800">CEO审批</h3>
                                <div id="detail-ceo" class="space-y-3"></div>
                            </div>
                        </div>

                        <!-- 附件管理标签页 -->
                        <div id="content-attachments" class="hidden space-y-4">
                            <!-- 初始附件 -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-lg mb-3 text-gray-800">初始附件</h3>
                                <div id="detail-attachments" class="space-y-2 max-h-[200px] overflow-y-auto pr-2"></div>
                            </div>

                            <!-- 审批附件 -->
                            <div class="bg-gray-50 p-4 rounded-lg">
                                <h3 class="font-semibold text-lg mb-3 text-gray-800">审批附件</h3>
                                <div id="detail-approval-attachments" class="space-y-2 max-h-[200px] overflow-y-auto pr-2"></div>
                            </div>

                            <!-- 上传新附件 (仅在审批时显示) -->
                            <div id="detail-upload-section" class="bg-blue-50 p-4 rounded-lg hidden">
                                <h3 class="font-semibold text-lg mb-3 text-blue-800">上传新附件</h3>
                                <input type="file" id="detail-new-attachments" multiple class="w-full p-2 border rounded-md">
                                <div id="detail-new-attachments-list" class="mt-2 space-y-2 max-h-[150px] overflow-y-auto"></div>
                                <button id="detail-confirm-attachments" class="mt-3 bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">确认附件</button>
                            </div>
                        </div>
                    </div>

                    <!-- 审批操作区域 (仅在审批时显示) -->
                    <div id="detail-approval-actions" class="hidden border-t p-4 bg-gray-50 flex-shrink-0">
                        <div class="space-y-3">
                            <h3 class="text-lg font-semibold text-gray-800">审批操作</h3>

                            <!-- 经理选择区域 (仅总监可见) - 优化版本 -->
                            <div id="detail-managers-selection" class="hidden space-y-2">
                                <!-- 可折叠标题栏 -->
                                <div class="flex items-center justify-between bg-blue-50 p-3 rounded-lg cursor-pointer" onclick="toggleManagersSelection()">
                                    <div class="flex items-center space-x-2">
                                        <svg id="managers-toggle-icon" class="w-5 h-5 text-blue-600 transform transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                        </svg>
                                        <label class="text-gray-700 font-medium cursor-pointer">选择下一阶段审批经理</label>
                                    </div>
                                    <span id="detail-selected-managers-summary" class="text-sm text-blue-600 font-medium">点击展开选择</span>
                                </div>

                                <!-- 可折叠内容区域 -->
                                <div id="managers-selection-content" class="hidden space-y-3 bg-gray-50 p-3 rounded-lg">
                                    <p class="text-sm text-blue-600">提示：如果不选择任何经理，申请将在您审批通过后流转给CEO进行最终审批</p>
                                    <div id="detail-managers-list" class="max-h-[120px] overflow-y-auto border rounded-md p-2 bg-white"></div>
                                    <div class="flex justify-between items-center mt-2">
                                        <span id="detail-selected-managers-count" class="text-sm text-gray-500">已选择 0 位经理</span>
                                        <button id="detail-confirm-managers" class="bg-green-600 text-white px-4 py-1 rounded-md hover:bg-green-700 transition-colors">确认选择</button>
                                    </div>
                                    <div id="detail-managers-confirm-status" class="mt-2 hidden">
                                        <p class="text-green-600 font-medium">✓ 已确认选择的经理</p>
                                    </div>
                                </div>
                            </div>

                            <!-- 审批备注 -->
                            <div>
                                <label class="block text-gray-700 mb-2">审批备注</label>
                                <textarea id="detail-approval-comment" class="w-full p-2 border rounded-md" rows="2" placeholder="请输入审批备注（可选）"></textarea>
                            </div>

                            <!-- 审批按钮 -->
                            <div class="flex space-x-4 justify-center">
                                <button id="detail-approve" class="bg-green-600 text-white px-6 py-2 rounded-md hover:bg-green-700">
                                    通过
                                </button>
                                <button id="detail-reject" class="bg-red-600 text-white px-6 py-2 rounded-md hover:bg-red-700">
                                    拒绝
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 文件预览模态框 -->
            <div id="previewModal" class="hidden fixed inset-0 bg-black bg-opacity-70 z-50 overflow-y-auto">
                <div class="relative mx-auto my-10 bg-white rounded-lg p-4 w-full max-w-4xl max-h-[90vh] flex flex-col">
                    <div class="flex justify-between items-center mb-2">
                        <h2 class="text-xl font-bold" id="previewFileName">文件预览</h2>
                        <button onclick="closePreview()" class="text-gray-500 hover:text-gray-700">✕</button>
                    </div>
                    <div class="flex-1 bg-gray-100 rounded overflow-hidden" style="min-height: 60vh;">
                        <!-- PC端PDF和文档预览 -->
                        <iframe id="previewFrame" class="w-full h-full border-0 hidden" style="min-height: 70vh;"></iframe>

                        <!-- 移动端PDF预览容器 -->
                        <div id="mobilePdfPreview" class="w-full h-full hidden relative" style="min-height: 70vh;">
                            <!-- PDF页面信息显示 -->
                            <div id="pdfNavigation" class="flex justify-center items-center p-2 bg-gray-200 border-b">
                                <span id="pageInfo" class="text-sm font-semibold">第 1 页，共 1 页</span>
                            </div>
                            <!-- PDF渲染容器 -->
                            <div id="pdfContainer" class="flex-1 overflow-auto p-2 bg-white" style="height: calc(100% - 40px);">
                                <canvas id="pdfCanvas" class="w-full border shadow-sm mx-auto block"></canvas>
                            </div>
                            <!-- 加载提示 -->
                            <div id="pdfLoadingIndicator" class="absolute inset-0 flex items-center justify-center bg-white bg-opacity-90">
                                <div class="text-center">
                                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                                    <div class="text-gray-600">正在加载PDF...</div>
                                </div>
                            </div>
                            <!-- 滑动提示 -->
                            <div id="swipeHint" class="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black bg-opacity-70 text-white px-3 py-1 rounded-full text-xs opacity-0 transition-opacity duration-300">
                                ← 滑动翻页 →
                            </div>
                        </div>

                        <!-- 图片预览 -->
                        <div id="imagePreview" class="w-full h-full flex items-center justify-center hidden" style="min-height: 70vh;">
                            <img id="previewImage" class="max-w-full max-h-full object-contain" alt="图片预览">
                        </div>
                        <!-- 不支持预览的文件类型 -->
                        <div id="unsupportedPreview" class="w-full h-full flex flex-col items-center justify-center hidden bg-gray-50" style="min-height: 70vh;">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                            </svg>
                            <p class="text-lg font-medium text-gray-600">无法预览此类型的文件</p>
                            <p class="text-sm text-gray-500 mt-2">请下载后查看</p>
                            <a id="unsupportedDownloadLink" href="#" class="mt-4 px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700 transition-colors" download>下载文件</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 申请书模板预览模态框 -->
            <div id="applicationTemplateModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 flex justify-center items-start overflow-y-auto hidden z-50 p-4">
                <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full relative">
                    <div class="p-4 border-b">
                        <h2 class="text-xl font-bold">申请书</h2>
                        <button onclick="closeApplicationTemplate()" class="absolute top-4 right-4 text-gray-500 hover:text-gray-700">
                            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>
                    <div class="p-4 overflow-auto" style="max-height: 80vh;">
                        <div class="a4-page" id="applicationTemplatePage">
                            <div id="applicationTemplateContent"></div>
                        </div>
                    </div>
                    <div class="p-4 border-t flex justify-end space-x-2 template-actions">
                        <button onclick="downloadApplicationTemplate()" class="bg-blue-500 hover:bg-blue-600 text-white px-4 py-2 rounded">
                            下载
                        </button>
                        <button onclick="closeApplicationTemplate()" class="bg-gray-300 hover:bg-gray-400 px-4 py-2 rounded">
                            关闭
                        </button>
                    </div>
                </div>
            </div>

            <!-- 编辑用户模态框 -->
            <div id="editUserModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 w-full max-w-lg">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold">编辑用户</h2>
                        <button onclick="closeEditUserModal()" class="text-gray-500 hover:text-gray-700">✕</button>
                    </div>
                    <form id="editUserForm" class="space-y-4">
                        <input type="hidden" id="editTargetUsername">

                        <div class="grid grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">用户代码</label>
                                <input type="text" id="editUserCode" class="w-full p-2 border rounded-md">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">角色</label>
                                <select id="editRole" class="w-full p-2 border rounded-md" onchange="toggleEditDepartment()">
                                    <option value="user">普通用户</option>
                                    <option value="readonly">用户(只读)</option>
                                    <option value="director">厂长</option>
                                    <option value="chief">总监</option>
                                    <option value="manager">经理</option>
                                    <option value="ceo">CEO</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-2 gap-4">
                            <div id="editDepartmentField">
                                <label class="block text-gray-700 mb-2">部门</label>
                                <select id="editUserDepartment" class="w-full p-2 border rounded-md">
                                    <option value="生产部">生产部</option>
                                    <option value="采购部">采购部</option>
                                    <option value="工程部">工程部</option>
                                    <option value="机电部">机电部</option>
                                    <option value="研发部">研发部</option>
                                    <option value="品管部">品管部</option>
                                    <option value="财务部">财务部</option>
                                    <option value="人事部">人事部</option>
                                    <option value="业务部">业务部</option>
                                    <option value="管理部">管理部</option>
                                    <option value="后勤部">后勤部</option>
                                    <option value="仓储部">仓储部</option>
                                    <option value="经理室">经理室</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">邮箱</label>
                                <input type="email" id="editEmail" class="w-full p-2 border rounded-md">
                            </div>
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-2">新密码</label>
                            <input type="password" id="editPassword" placeholder="留空则不修改" class="w-full p-2 border rounded-md">
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-2">电子签名</label>
                            <input type="file" id="editSignature" accept="image/*" class="w-full p-2 border rounded-md">
                            <div id="editSignaturePreview" class="mt-2 hidden">
                                <img id="editSignatureImage" src="" alt="电子签名预览" class="max-h-16">
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 mt-4">
                            <button type="button" onclick="closeEditUserModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">取消</button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">保存</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 添加新用户模态框 -->
            <div id="addUserModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
                <div class="bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-y-auto">
                    <div class="flex justify-between items-center mb-4">
                        <h2 class="text-xl font-bold">添加新用户</h2>
                        <button onclick="closeAddUserModal()" class="text-gray-500 hover:text-gray-700">✕</button>
                    </div>
                    <form id="addUserForm" class="space-y-4">
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">用户名</label>
                                <input type="text" id="newUsername" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">密码</label>
                                <input type="password" id="newPassword" required class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">用户代码</label>
                                <input type="text" id="newUserCode" class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div>
                                <label class="block text-gray-700 mb-2">角色</label>
                                <select id="newUserRole" class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                                    <option value="user">普通用户</option>
                                    <option value="readonly">用户(只读)</option>
                                    <option value="director">厂长</option>
                                    <option value="chief">总监</option>
                                    <option value="manager">经理</option>
                                    <option value="ceo">CEO</option>
                                    <option value="admin">管理员</option>
                                </select>
                            </div>
                        </div>

                        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                                <label class="block text-gray-700 mb-2">邮箱（可选）</label>
                                <input type="email" id="newUserEmail" class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                            </div>
                            <div id="newUserDepartmentField">
                                <label class="block text-gray-700 mb-2">部门</label>
                                <select id="newUserDepartment" class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                                    <option value="生产部">生产部</option>
                                    <option value="采购部">采购部</option>
                                    <option value="工程部">工程部</option>
                                    <option value="机电部">机电部</option>
                                    <option value="研发部">研发部</option>
                                    <option value="品管部">品管部</option>
                                    <option value="财务部">财务部</option>
                                    <option value="人事部">人事部</option>
                                    <option value="业务部">业务部</option>
                                    <option value="管理部">管理部</option>
                                    <option value="后勤部">后勤部</option>
                                    <option value="仓储部">仓储部</option>
                                    <option value="经理室">经理室</option>
                                </select>
                            </div>
                        </div>

                        <div>
                            <label class="block text-gray-700 mb-2">电子签名 <span class="text-sm text-gray-500">(上传图片)</span></label>
                            <input type="file" id="newUserSignature" accept="image/*" class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500">
                            <div id="signaturePreview" class="mt-2 hidden">
                                <img id="signatureImage" src="" alt="电子签名预览" class="max-h-16">
                                <button type="button" id="clearSignature" class="text-red-500 text-sm mt-1">清除</button>
                            </div>
                        </div>

                        <div class="flex justify-end space-x-3 mt-4">
                            <button type="button" onclick="closeAddUserModal()" class="px-4 py-2 bg-gray-300 text-gray-800 rounded hover:bg-gray-400">取消</button>
                            <button type="submit" class="px-4 py-2 bg-blue-600 text-white rounded hover:bg-blue-700">添加用户</button>
                        </div>
                    </form>
                </div>
            </div>
                </main>
            </div>
        </div>

        <!-- 半透明遮罩层 - 仅在移动端显示 -->
        <div id="menuOverlay" class="fixed inset-0 bg-black bg-opacity-30 backdrop-blur-sm z-20 hidden" onclick="toggleMenu()"></div>
    </div>



    <script>
        let currentUser = null;
        let currentRole = null;
        let currentDepartment = null;
        let applications = [];
        let allUsers = [];
        let currentUserPage = 1;
        let usersPerPage = 10;
        // 删除模式标志
        let deleteMode = false;

        // 角色映射
        const roleMap = {
            'admin': '管理员',
            'user': '普通用户',
            'director': '厂长',
            'chief': '总监',
            'manager': '经理',
            'ceo': 'CEO',
            'readonly': '用户(只读)'
        };

        // 优先级映射
        const priorityMap = {
            'normal': '普通',
            'medium': '中等',
            'high': '紧急'
        };

        // 审批状态的中文映射
        const statusMap = {
            'pending': '待审批',
            'approved': '通过',
            'rejected': '拒绝'
        };

        // 获取动态申请状态说明
        function getApplicationStatusDesc(status, app) {
            switch (status) {
                case '待厂长审核':
                    return '等待选中的厂长审批';

                case '待总监审批':
                    // 检查是否经过了厂长审批
                    const hasDirectorApprovals = app && app.approvals && app.approvals.directors &&
                        Object.keys(app.approvals.directors).length > 0 &&
                        Object.values(app.approvals.directors).some(d => d.status === 'approved');

                    if (hasDirectorApprovals) {
                        return '所有厂长已审批通过，等待总监审批';
                    } else {
                        return '等待总监审批';
                    }

                case '待经理审批':
                    // 检查审批流程
                    const hasDirectorApprovalsForManager = app && app.approvals && app.approvals.directors &&
                        Object.values(app.approvals.directors).some(d => d.status === 'approved');
                    const hasChiefApprovalForManager = app && app.approvals && app.approvals.chief &&
                        app.approvals.chief.status === 'approved';

                    if (hasDirectorApprovalsForManager && hasChiefApprovalForManager) {
                        return '总监已审批通过，等待选中的经理审批';
                    } else if (hasChiefApprovalForManager) {
                        return '总监已审批通过，等待选中的经理审批';
                    } else {
                        return '等待选中的经理审批';
                    }

                case '待CEO审批':
                    // 检查审批流程
                    const hasDirectorApprovalsForCEO = app && app.approvals && app.approvals.directors &&
                        Object.values(app.approvals.directors).some(d => d.status === 'approved');
                    const hasChiefApprovalForCEO = app && app.approvals && app.approvals.chief &&
                        app.approvals.chief.status === 'approved';
                    const hasManagerApprovals = app && app.approvals && app.approvals.managers &&
                        Object.keys(app.approvals.managers).length > 0 &&
                        Object.values(app.approvals.managers).some(m => m.status === 'approved');

                    if (hasManagerApprovals) {
                        return '经理已审批通过，等待CEO最终审批';
                    } else {
                        return '等待CEO最终审批';
                    }

                case '已通过':
                    return '审批流程已完成，申请通过';

                case '已拒绝':
                    return '申请被拒绝';

                default:
                    return status;
            }
        }

        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            console.log('页面加载完成');

            // 添加网络恢复监听器
            window.addEventListener('networkRecovered', () => {
                console.log('网络已恢复，重新加载数据...');
                showNetworkStatus('网络已恢复，正在重新加载数据...', 'success');

                // 重新加载所有数据
                setTimeout(() => {
                    loadApplications(true, true); // 使用居中加载指示器
                    loadAllUsers();
                    if (currentRole === 'admin') {
                        loadUsers();
                    }
                }, 1000);
            });

            // 初始化编辑用户表单事件监听
            document.getElementById('editUserForm').addEventListener('submit', handleEditUserSubmit);

            // 初始化修改密码相关事件监听
            const changePasswordBtn = document.getElementById('changePasswordBtn');
            const cancelChangePasswordBtn = document.getElementById('cancelChangePassword');
            const changePasswordForm = document.getElementById('changePasswordForm');

            console.log('修改密码按钮:', changePasswordBtn);
            console.log('取消按钮:', cancelChangePasswordBtn);
            console.log('修改密码表单:', changePasswordForm);

            if (changePasswordBtn) {
                changePasswordBtn.addEventListener('click', showChangePasswordModal);
            }

            if (cancelChangePasswordBtn) {
                cancelChangePasswordBtn.addEventListener('click', hideChangePasswordModal);
            }

            if (changePasswordForm) {
                changePasswordForm.addEventListener('submit', handleChangePassword);
            }

            // 检查是否已登录 - 只使用sessionStorage确保会话级别存储
            if (sessionStorage.getItem('isLoggedIn') === 'true') {
                currentUser = sessionStorage.getItem('username');
                currentRole = sessionStorage.getItem('role');
                currentDepartment = sessionStorage.getItem('department');

                // 显示主应用界面但不处理页面导航（页面导航由主DOMContentLoaded处理）
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');

                updateUserDisplay();
                updateNavButtons();

                // 优化加载顺序 - 先加载关键数据，再加载次要数据
                loadApplications().then(() => {
                    // 隐藏加载指示器，让用户感觉加载更快
                    hideCenterLoadingIndicator();

                    // 异步加载用户数据，不阻塞主界面显示
                    loadAllUsers().then(() => {
                        // 确保签名信息已被提取
                        if (!allUsers || allUsers.length === 0) {
                            extractSignaturesFromApplications();
                        }
                        console.log('用户数据加载完成，已提取签名信息:', allUsers);
                    }).catch(error => {
                        console.warn('用户数据加载失败，但不影响主要功能:', error);
                    });
                });

                // 异步加载审批人数据
                loadApprovers().catch(error => {
                    console.warn('审批人数据加载失败:', error);
                });

                // 管理员用户异步加载完整的用户数据
                if (currentRole === 'admin') {
                    loadUsers().catch(error => {
                        console.warn('管理员用户数据加载失败:', error);
                    });
                }



                // 注意：页面导航逻辑已移至主DOMContentLoaded事件处理器中
                // 这里不再处理页面导航，避免与主逻辑冲突
            } else {
                showLoginForm();
            }

            // 确保PC端顶部导航菜单正确显示
            if (window.innerWidth >= 768) {
                fixPCNavigation();
            }
        });

        // 加载所有用户数据（用于获取电子签名）- 增强版，支持代理和VPN网络
        async function loadAllUsers() {
            try {
                // 如果是管理员，直接获取所有用户数据
                if (currentRole === 'admin') {
                    try {
                        const users = await apiRequest(`/users?username=${currentUser}`, {
                            method: 'GET',
                            timeout: 6000, // 减少超时时间
                            retry: {
                                maxRetries: 2, // 减少重试次数
                                retryDelay: 500, // 减少重试延迟
                                retryStatusCodes: [0, 408, 429, 500, 502, 503, 504]
                            }
                        });

                        // 验证数据格式
                        if (!Array.isArray(users)) {
                            throw new Error('服务器返回的用户数据格式不正确');
                        }

                        // 如果allUsers已有数据，合并新数据，保留已有的签名信息
                        if (allUsers && allUsers.length > 0) {
                            // 遍历新获取的用户数据
                            users.forEach(newUser => {
                                // 查找是否已存在该用户
                                const existingUser = allUsers.find(u => u.username === newUser.username);
                                if (existingUser) {
                                    // 如果新用户有签名而现有用户没有，更新签名
                                    if (newUser.signature && !existingUser.signature) {
                                        existingUser.signature = newUser.signature;
                                    }
                                } else {
                                    // 如果是新用户，添加到allUsers
                                    allUsers.push(newUser);
                                }
                            });
                        } else {
                            // 如果allUsers为空，直接赋值
                            allUsers = users;
                        }
                    } catch (error) {
                        console.error('获取用户数据失败:', error);
                        // 尝试从申请中提取签名信息
                        extractSignaturesFromApplications();
                    }
                } else {
                    // 非管理员用户，使用新的signatures接口获取所有用户的签名数据
                    try {
                        const signatures = await apiRequest(`/signatures?username=${currentUser}`, {
                            method: 'GET',
                            timeout: 15000,
                            retry: {
                                maxRetries: 3,
                                retryDelay: 1500,
                                retryStatusCodes: [0, 408, 429, 500, 502, 503, 504]
                            }
                        });

                        // 验证数据格式
                        if (!Array.isArray(signatures)) {
                            throw new Error('服务器返回的签名数据格式不正确');
                        }

                        // 如果allUsers已有数据，合并新数据，保留已有的签名信息
                        if (allUsers && allUsers.length > 0) {
                            // 遍历新获取的用户数据
                            signatures.forEach(newUser => {
                                // 查找是否已存在该用户
                                const existingUser = allUsers.find(u => u.username === newUser.username);
                                if (existingUser) {
                                    // 如果新用户有签名而现有用户没有，更新签名
                                    if (newUser.signature && !existingUser.signature) {
                                        existingUser.signature = newUser.signature;
                                    }
                                } else {
                                    // 如果是新用户，添加到allUsers
                                    allUsers.push(newUser);
                                }
                            });
                        } else {
                            // 如果allUsers为空，直接赋值
                            allUsers = signatures;
                        }

                        console.log('成功获取所有用户的签名数据:', allUsers);
                    } catch (error) {
                        console.error('获取签名数据出错:', error);
                        // 尝试从申请中提取签名信息
                        extractSignaturesFromApplications();
                    }
                }

                return allUsers;
            } catch (error) {
                console.error('加载用户数据出错:', error);
                // 尝试从申请中提取签名信息
                extractSignaturesFromApplications();
                return allUsers;
            }
        }

        // 从应用程序数据中提取签名信息
        function extractSignaturesFromApplications() {
            if (!applications || applications.length === 0) return;

            // 创建临时用户数据存储
            allUsers = allUsers || [];

            console.log('开始从应用程序中提取签名信息...');

            // 遍历所有应用程序，提取签名信息
            applications.forEach(app => {
                if (app.approvals) {
                    // 提取厂长签名
                    if (app.approvals.directors) {
                        Object.entries(app.approvals.directors).forEach(([username, approval]) => {
                            // 检查是否已存在该用户
                            const existingUser = allUsers.find(u => u.username === username);

                            // 如果用户不存在或者用户存在但没有签名而当前审批有签名
                            if (approval.signature && (!existingUser || !existingUser.signature)) {
                                if (existingUser) {
                                    // 更新现有用户的签名
                                    existingUser.signature = approval.signature;
                                    console.log(`更新厂长 ${username} 的签名`);
                } else {
                                    // 添加新用户
                                    allUsers.push({
                                        username: username,
                                        role: 'director',
                                        signature: approval.signature
                                    });
                                    console.log(`添加厂长 ${username} 及其签名`);
                                }
                            }
                        });
                    }

                    // 提取总监签名
                    if (app.approvals.chief && app.approvals.chief.signature) {
                        const chiefUsername = app.approvals.chief.username || 'chief';
                        // 检查是否已存在总监用户
                        const existingChief = allUsers.find(u => u.username === chiefUsername || u.role === 'chief');

                        if (existingChief) {
                            // 更新现有总监的签名
                            if (!existingChief.signature) {
                                existingChief.signature = app.approvals.chief.signature;
                                console.log(`更新总监 ${existingChief.username} 的签名`);
                            }
                        } else {
                            // 添加新总监用户
                            allUsers.push({
                                username: chiefUsername,
                                role: 'chief',
                                signature: app.approvals.chief.signature
                            });
                            console.log(`添加总监 ${chiefUsername} 及其签名`);
                        }
                    }

                    // 提取经理签名
                    if (app.approvals.managers) {
                        Object.entries(app.approvals.managers).forEach(([username, approval]) => {
                            // 检查是否已存在该用户
                            const existingUser = allUsers.find(u => u.username === username);

                            // 如果用户不存在或者用户存在但没有签名而当前审批有签名
                            if (approval.signature && (!existingUser || !existingUser.signature)) {
                                if (existingUser) {
                                    // 更新现有用户的签名
                                    existingUser.signature = approval.signature;
                                    console.log(`更新经理 ${username} 的签名`);
                                } else {
                                    // 添加新用户
                                    allUsers.push({
                                        username: username,
                                        role: 'manager',
                                        signature: approval.signature
                                    });
                                    console.log(`添加经理 ${username} 及其签名`);
                                }
                            }
                        });
                    }

                    // 提取CEO签名 - 增强版，确保历史数据也能正确处理
                    if (app.approvals.ceo && app.approvals.ceo.signature) {
                        // 尝试多种方式获取CEO用户名，确保兼容历史数据
                        const ceoUsername = app.approvals.ceo.approverUsername ||
                                           app.approvals.ceo.username ||
                                           'ceo';

                        // 检查是否已存在CEO用户（多种匹配方式）
                        let existingCeo = allUsers.find(u =>
                            u.username === ceoUsername ||
                            u.role === 'ceo' ||
                            (u.username && u.username.toLowerCase().includes('ceo')) ||
                            (ceoUsername !== 'ceo' && u.username === ceoUsername)
                        );

                        if (existingCeo) {
                            // 更新现有CEO的签名（如果当前没有签名或者新签名更新）
                            if (!existingCeo.signature ||
                                (app.approvals.ceo.signature && app.approvals.ceo.signature !== existingCeo.signature)) {
                                existingCeo.signature = app.approvals.ceo.signature;
                                console.log(`更新CEO ${existingCeo.username} 的签名`);
                            }
                        } else {
                            // 添加新CEO用户
                            allUsers.push({
                                username: ceoUsername,
                                role: 'ceo',
                                signature: app.approvals.ceo.signature
                            });
                            console.log(`添加CEO ${ceoUsername} 及其签名`);
                            console.log(`CEO签名数据长度: ${app.approvals.ceo.signature ? app.approvals.ceo.signature.length : 0}`);
                        }
                    }

                    // 提取申请人签名（如果有）
                    if (app.signature) {
                        const existingUser = allUsers.find(u => u.username === app.applicant);
                        if (existingUser) {
                            if (!existingUser.signature) {
                                existingUser.signature = app.signature;
                                console.log(`更新申请人 ${app.applicant} 的签名`);
                            }
                        } else {
                            allUsers.push({
                                username: app.applicant,
                                role: 'user',
                                signature: app.signature
                            });
                            console.log(`添加申请人 ${app.applicant} 及其签名`);
                        }
                    }
                }
            });

            console.log('从应用程序中提取的用户签名数据:', allUsers);
        }

        function showLoginForm() {
            document.getElementById('loginPage').classList.remove('hidden');
            document.getElementById('mainApp').classList.add('hidden');
            document.getElementById('loginError').classList.add('hidden');
        }

        // 确保PC端顶部导航菜单正确显示
        function fixPCNavigation() {
            if (window.innerWidth >= 768) {
                const topNavMenu = document.querySelector('.hidden.md\\:block');
                if (topNavMenu) {
                    // 强制显示顶部导航菜单
                    topNavMenu.classList.remove('hidden');
                    topNavMenu.style.display = 'flex';
                }
            }
        }

        // 显示主应用界面
        function showMainApp() {
            document.getElementById('loginPage').classList.add('hidden');
            document.getElementById('mainApp').classList.remove('hidden');

            // 在移动端默认隐藏侧边栏，PC端不需要显示侧边栏
            const sideMenu = document.getElementById('sideMenu');
            if (window.innerWidth < 768) {
                sideMenu.classList.add('-translate-x-full');
                document.getElementById('menuOverlay').classList.add('hidden');
            }

            // 确保PC端顶部导航菜单正确显示
            fixPCNavigation();

            let targetSection;

            // 登录时始终跳转到默认首页
            if (currentRole === 'admin') {
                // 管理员默认显示用户管理页面
                targetSection = 'manageUsers';
            } else if (['director', 'chief', 'manager', 'ceo'].includes(currentRole)) {
                // 所有审批人默认显示待审核页面
                targetSection = 'pendingApproval';
            } else {
                // 普通用户默认显示申请记录页面
                targetSection = 'history';
            }

            // 只保存到sessionStorage，确保浏览器关闭后页面状态被清除
            sessionStorage.setItem('currentSection', targetSection);

            // 显示页面
            showSection(targetSection);
            initFileUpload();

            // 设置申请日期默认为当前日期
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('applyDate').value = today;

            // 设置申请人姓名为当前用户
            document.getElementById('applicant').value = currentUser;

            // 设置申请部门为当前用户的部门
            if (currentDepartment) {
                const departmentInput = document.getElementById('department');
                if (departmentInput) {
                    departmentInput.value = currentDepartment;
                }
            }
        }

        function updateUserDisplay() {
            // 使用新的updateUserInfo函数更新用户信息
            updateUserInfo();
        }

        function initFileUpload() {
            const fileInput = document.getElementById('attachments');
            const dropZone = document.getElementById('fileInputArea');
            const editFileInput = document.getElementById('editAttachments');
            const editDropZone = document.getElementById('editFileInputArea');

            // 检测是否为移动设备
            const isMobile = window.innerWidth <= 768;

            fileInput.addEventListener('change', handleFileSelect);

            // 优化的拖拽功能
            dropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                dropZone.classList.add('border-blue-500');
                if (isMobile) {
                    dropZone.parentElement.classList.add('drag-over');
                }
            });

            dropZone.addEventListener('dragleave', () => {
                dropZone.classList.remove('border-blue-500');
                if (isMobile) {
                    dropZone.parentElement.classList.remove('drag-over');
                }
            });

            dropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                dropZone.classList.remove('border-blue-500');
                if (isMobile) {
                    dropZone.parentElement.classList.remove('drag-over');
                }
                fileInput.files = e.dataTransfer.files;
                handleFileSelect();
            });

            // 移动端触摸优化
            if (isMobile) {
                dropZone.addEventListener('touchstart', (e) => {
                    dropZone.style.transform = 'scale(0.98)';
                    dropZone.style.transition = 'transform 0.1s ease';
                });

                dropZone.addEventListener('touchend', (e) => {
                    dropZone.style.transform = 'scale(1)';
                    setTimeout(() => {
                        dropZone.style.transition = '';
                    }, 100);
                });
            }

            // 编辑文件上传功能保持不变
            if (editFileInput && editDropZone) {
                editFileInput.addEventListener('change', handleEditFileSelect);
                editDropZone.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    editDropZone.classList.add('border-blue-500');
                });
                editDropZone.addEventListener('dragleave', () => {
                    editDropZone.classList.remove('border-blue-500');
                });
                editDropZone.addEventListener('drop', (e) => {
                    e.preventDefault();
                    editDropZone.classList.remove('border-blue-500');
                    editFileInput.files = e.dataTransfer.files;
                    handleEditFileSelect();
                });
            }
        }

        function showSection(section) {
            // 更新移动端侧边栏菜单的活动状态
            document.querySelectorAll('.side-nav-btn').forEach(btn => {
                if (btn.dataset.section === section) {
                    btn.classList.add('active-indicator');
                } else {
                    btn.classList.remove('active-indicator');
                }
            });

            // 更新PC端侧边栏菜单的活动状态
            document.querySelectorAll('.pc-nav-btn').forEach(btn => {
                if (btn.dataset.section === section) {
                    btn.classList.add('active-indicator');
                } else {
                    btn.classList.remove('active-indicator');
                }
            });

            // 更新顶部导航菜单的活动状态（保留兼容性）
            document.querySelectorAll('.nav-btn[data-section]').forEach(btn => {
                if (btn.dataset.section === section) {
                    btn.classList.add('active');
                } else {
                    btn.classList.remove('active');
                }
            });



            document.getElementById('newSection').classList.toggle('hidden', section !== 'new');
            document.getElementById('historySection').classList.toggle('hidden', section !== 'history');
            document.getElementById('pendingApprovalSection').classList.toggle('hidden', section !== 'pendingApproval');
            document.getElementById('approvedSection').classList.toggle('hidden', section !== 'approved');

            document.getElementById('systemSettingsSection').classList.toggle('hidden', section !== 'systemSettings' || currentRole !== 'admin');
            document.getElementById('manageUsersSection').classList.toggle('hidden', section !== 'manageUsers' || currentRole !== 'admin');

            if (section === 'history') updateHistoryList();
            if (section === 'pendingApproval') updatePendingApprovalList();
            if (section === 'approved') updateApprovedList();

            if (section === 'systemSettings' && currentRole === 'admin') loadSystemSettings();
            if (section === 'manageUsers' && currentRole === 'admin') loadUsers();

            // 如果切换到新建申请页面，确保申请人和申请部门始终为当前用户
            if (section === 'new') {
                // 设置申请日期默认为当前日期
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('applyDate').value = today;

                // 设置申请人姓名为当前用户
                document.getElementById('applicant').value = currentUser;

                // 设置申请部门为当前用户的部门
                if (currentDepartment) {
                    const departmentInput = document.getElementById('department');
                    if (departmentInput) {
                        departmentInput.value = currentDepartment;
                    }
                }
            }

            // 只保存到sessionStorage，确保浏览器关闭后页面状态被清除
            sessionStorage.setItem('currentSection', section);
        }

        // 根据用户角色跳转到相应的主页
        function goToHomePage() {
            if (currentRole === 'admin') {
                // 管理员跳转到用户管理页面
                showSection('manageUsers');
            } else if (['director', 'chief', 'manager', 'ceo'].includes(currentRole)) {
                // 所有审批人跳转到待审核页面
                showSection('pendingApproval');
            } else {
                // 普通用户跳转到申请记录页面
                showSection('history');
            }
        }

        function handleFileSelect() {
            const files = Array.from(document.getElementById('attachments').files);
            const validFiles = files.filter(file =>
                file.size <= 5 * 1024 * 1024 && /\.(pdf|doc|docx|jpg|jpeg|png)$/i.test(file.name)
            );
            const invalidFiles = files.filter(file => !validFiles.includes(file));
            if (invalidFiles.length > 0) {
                // 移动端优化的错误提示
                if (window.innerWidth <= 768) {
                    showMobileAlert(`以下文件不符合要求：\n${invalidFiles.map(f => f.name).join('\n')}`);
                } else {
                    alert(`以下文件不符合要求：\n${invalidFiles.map(f => f.name).join('\n')}`);
                }
            }

            // 如果有有效文件，弹出文件命名规范提醒
            if (validFiles.length > 0) {
                if (window.innerWidth <= 768) {
                    showMobileAlert('请确认您上传的附件文档名称符合基本文件描述规范');
                } else {
                    alert('请确认您上传的附件文档名称符合基本文件描述规范');
                }
            }

            updateFileList(validFiles);
        }

        // 移动端优化的提示框
        function showMobileAlert(message) {
            // 移除已存在的提示框
            const existingAlert = document.querySelector('.mobile-alert');
            if (existingAlert) {
                existingAlert.remove();
            }

            const alertDiv = document.createElement('div');
            alertDiv.className = 'mobile-alert fixed top-4 left-4 right-4 bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded-lg z-50 shadow-lg';
            alertDiv.innerHTML = `
                <div class="flex justify-between items-start">
                    <span class="text-sm whitespace-pre-line">${message}</span>
                    <button onclick="this.parentElement.parentElement.remove()" class="text-red-700 font-bold ml-2 text-lg leading-none">×</button>
                </div>
            `;
            document.body.appendChild(alertDiv);

            // 3秒后自动消失
            setTimeout(() => {
                if (alertDiv.parentElement) {
                    alertDiv.style.opacity = '0';
                    alertDiv.style.transform = 'translateY(-10px)';
                    alertDiv.style.transition = 'all 0.3s ease';
                    setTimeout(() => {
                        alertDiv.remove();
                    }, 300);
                }
            }, 3000);
        }

        function handleEditFileSelect() {
            const files = Array.from(document.getElementById('editAttachments').files);
            const validFiles = files.filter(file =>
                file.size <= 5 * 1024 * 1024 && /\.(pdf|doc|docx|jpg|jpeg|png)$/i.test(file.name)
            );
            const invalidFiles = files.filter(file => !validFiles.includes(file));
            if (invalidFiles.length > 0) {
                alert(`以下文件不符合要求：\n${invalidFiles.map(f => f.name).join('\n')}`);
        }

            // 如果有有效文件，弹出文件命名规范提醒
            if (validFiles.length > 0) {
                alert('请确认您上传的附件文档名称符合基本文件描述规范');
            }

            // 直接显示已选择的文件列表
            const editFileList = document.getElementById('editFileList');
            editFileList.innerHTML = validFiles.map((file, index) => `
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm">${sanitizeInput(file.name)}</span>
                        <span class="text-xs text-gray-400">${(file.size/1024/1024).toFixed(2)}MB</span>
                    </div>
                    <button onclick="removeEditFile(${index})" class="text-red-500 hover:text-red-700 text-sm">移除</button>
                </div>
            `).join('');
        }

        function updateFileList(files) {
            document.getElementById('fileList').innerHTML = files.map((file, index) => `
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm">${sanitizeInput(file.name)}</span>
                        <span class="text-xs text-gray-400">${(file.size/1024/1024).toFixed(2)}MB</span>
                    </div>
                    <button onclick="removeFile(${index})" class="text-red-500 hover:text-red-700 text-sm">移除</button>
                </div>
            `).join('');
        }

        function removeFile(index) {
            const dt = new DataTransfer();
            const files = Array.from(document.getElementById('attachments').files);
            files.splice(index, 1);
            files.forEach(f => dt.items.add(f));
            document.getElementById('attachments').files = dt.files;
            handleFileSelect();
        }

        function removeEditFile(index) {
            const dt = new DataTransfer();
            const files = Array.from(document.getElementById('editAttachments').files);
            files.splice(index, 1);
            files.forEach(f => dt.items.add(f));
            document.getElementById('editAttachments').files = dt.files;
            handleEditFileSelect();
        }

        document.getElementById('loginForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('loginUsername').value;
            const password = document.getElementById('loginPassword').value;

            try {
                // 显示登录状态
                showNetworkStatus('正在登录...', 'loading');

                // 使用增强的API请求工具
                const result = await apiRequest('/login', {
                    method: 'POST',
                    data: { username, password },
                    timeout: 10000,
                    retry: {
                        maxRetries: 2,
                        retryDelay: 1000,
                        retryStatusCodes: [0, 408, 500, 502, 503, 504]
                    }
                });

                // 隐藏登录状态
                hideNetworkStatus();

                if (result.success) {
                    currentUser = result.username; // 使用服务器返回的用户名
                    currentRole = result.role;
                    currentDepartment = result.department; // 保存用户部门信息

                    // 使用sessionStorage确保浏览器关闭后需要重新登录
                    sessionStorage.setItem('isLoggedIn', 'true');
                    sessionStorage.setItem('username', result.username);
                    sessionStorage.setItem('role', result.role);
                    sessionStorage.setItem('department', result.department || '');

                    // 记录登录时间戳和最后活动时间
                    const currentTime = Date.now();
                    sessionStorage.setItem('loginTimestamp', currentTime.toString());
                    sessionStorage.setItem('lastActivity', currentTime.toString());

                    // 启动会话监控
                    startSessionMonitoring();

                    showMainApp();
                    updateUserDisplay();
                    updateNavButtons();
                    loadApplications();
                    loadApprovers();


                } else {
                    document.getElementById('loginError').textContent = result.message;
                    document.getElementById('loginError').classList.remove('hidden');
                }
            } catch (error) {
                console.error('登录失败:', error);
                hideNetworkStatus();

                // 根据错误类型提供不同的提示
                let errorMessage = '登录失败，请稍后再试';
                if (error.status === 'TIMEOUT') {
                    errorMessage = '登录超时，请检查网络连接';
                } else if (error.message && error.message.includes('network')) {
                    errorMessage = '网络连接异常，请检查网络设置';
                }

                document.getElementById('loginError').textContent = errorMessage;
                document.getElementById('loginError').classList.remove('hidden');
            }
        });

        document.getElementById('addUserForm').addEventListener('submit', async (e) => {
            e.preventDefault();
            const username = document.getElementById('newUsername').value;
            const password = document.getElementById('newPassword').value;
            const role = document.getElementById('newUserRole').value;
            const email = document.getElementById('newUserEmail').value;
            const userCode = document.getElementById('newUserCode').value;

            // 验证添加用户表单
            const addUserValidationResult = validateAddUserForm(username, password, role, email, userCode);
            if (!addUserValidationResult.isValid) {
                showValidationError(addUserValidationResult.errors);
                return;
            }

            // 如果是总监，设置部门为"经理室"，如果是其他管理员、经理或CEO则为空，其他角色使用选择的部门
            let department = '';
            if (role === 'chief') {
                department = '经理室';
            } else if (role === 'manager' || role === 'admin' || role === 'ceo') {
                department = '';
            } else {
                department = document.getElementById('newUserDepartment').value;
                // 验证部门字段（对于需要部门的角色）
                if (!department || department.trim() === '') {
                    showValidationError(['请选择用户部门']);
                    return;
                }
            }
            const signatureFile = document.getElementById('newUserSignature').files[0];

            try {
                // 如果有签名文件，先转换为base64
                let signatureData = null;
                if (signatureFile) {
                    signatureData = await readFileAsDataURL(signatureFile);
                }

                const response = await fetch('/addUser', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        adminUsername: currentUser,
                        username,
                        password,
                        role,
                        email,
                        department,
                        userCode,
                        signature: signatureData
                    })
                });
                const result = await response.json();
                if (result.success) {
                    alert('用户添加成功');
                    closeAddUserModal(); // 关闭模态框
                    await loadUsers();
                    } else {
                    alert('用户添加失败：' + result.message);
                }
            } catch (error) {
                console.error('添加用户失败:', error);
                alert('添加用户失败');
            }
        });

        // 自定义确认对话框函数
        function showCustomConfirm(message, inputField) {
            return new Promise((resolve) => {
                // 设置消息（支持HTML内容）
                document.getElementById('customConfirmMessage').innerHTML = message;

                // 显示对话框
                const modal = document.getElementById('customConfirmModal');
                modal.classList.remove('hidden');

                // 确认按钮事件
                const yesButton = document.getElementById('customConfirmYes');
                const noButton = document.getElementById('customConfirmNo');

                // 清除之前可能存在的事件监听器
                const newYesButton = yesButton.cloneNode(true);
                const newNoButton = noButton.cloneNode(true);
                yesButton.parentNode.replaceChild(newYesButton, yesButton);
                noButton.parentNode.replaceChild(newNoButton, noButton);

                // 添加新的事件监听器
                newYesButton.addEventListener('click', () => {
                    modal.classList.add('hidden');
                    resolve(true);
                });

                newNoButton.addEventListener('click', () => {
                    modal.classList.add('hidden');
                    if (inputField) {
                        inputField.focus();
                    }
                    resolve(false);
                });
            });
        }

        // 读取文件为DataURL (base64)
        function readFileAsDataURL(file) {
            return new Promise((resolve, reject) => {
                // 检查文件大小
                const maxSizeMB = 5; // 最大5MB
                const maxSizeBytes = maxSizeMB * 1024 * 1024;

                if (file.size > maxSizeBytes) {
                    reject(new Error(`签名图片过大，请上传小于${maxSizeMB}MB的图片`));
                    return;
                }

                // 检查文件类型
                if (!file.type.startsWith('image/')) {
                    reject(new Error('请上传图片格式的签名文件'));
                    return;
                }

                const reader = new FileReader();

                // 设置超时
                const timeoutId = setTimeout(() => {
                    reader.abort();
                    reject(new Error('读取文件超时，请尝试使用更小的图片'));
                }, 10000); // 10秒超时

                reader.onload = () => {
                    clearTimeout(timeoutId);

                    // 检查结果是否有效
                    if (typeof reader.result !== 'string' || !reader.result.startsWith('data:image/')) {
                        reject(new Error('文件格式无效'));
                        return;
                    }

                    // 检查数据大小
                    if (reader.result.length > 5000000) { // 如果大于5MB
                        console.warn('签名数据过大:', reader.result.length, '字节');
                        // 这里可以添加压缩逻辑
                    }

                    resolve(reader.result);
                };

                reader.onerror = (error) => {
                    clearTimeout(timeoutId);
                    console.error('读取文件失败:', error);
                    reject(new Error('读取文件失败'));
                };

                reader.onabort = () => {
                    clearTimeout(timeoutId);
                    reject(new Error('读取文件被中止'));
                };

                // 开始读取文件
                try {
                    reader.readAsDataURL(file);
                } catch (error) {
                    clearTimeout(timeoutId);
                    console.error('启动文件读取失败:', error);
                    reject(new Error('无法读取文件: ' + error.message));
                }
            });
        }

        // 电子签名预览
        document.getElementById('newUserSignature').addEventListener('change', function(e) {
            const file = e.target.files[0];
            const preview = document.getElementById('signaturePreview');
            const image = document.getElementById('signatureImage');

            if (file) {
                const reader = new FileReader();
                reader.onload = function(event) {
                    image.src = event.target.result;
                    preview.classList.remove('hidden');
                };
                reader.readAsDataURL(file);
            } else {
                preview.classList.add('hidden');
            }
        });

        // 清除电子签名
        document.getElementById('clearSignature').addEventListener('click', function() {
            document.getElementById('newUserSignature').value = '';
            document.getElementById('signaturePreview').classList.add('hidden');
        });

        document.getElementById('applicationForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            // 严格验证所有必填字段
            const validationResult = validateApplicationForm();
            if (!validationResult.isValid) {
                // 显示验证错误信息
                showValidationError(validationResult.errors);
                return;
            }

            // 显示"正在提交"状态
            const submitBtn = document.getElementById('submitBtn');
            const submitBtnText = document.getElementById('submitBtnText');
            const submitBtnSpinner = document.getElementById('submitBtnSpinner');

            // 保存原始状态
            const originalText = submitBtnText.textContent;
            const originalDisabled = submitBtn.disabled;

            // 设置提交中状态
            submitBtn.disabled = true;
            submitBtnText.textContent = '正在提交...';
            submitBtnSpinner.classList.remove('hidden');

            // 定义恢复按钮状态的函数
            const restoreButtonState = () => {
                submitBtn.disabled = originalDisabled;
                submitBtnText.textContent = originalText;
                submitBtnSpinner.classList.add('hidden');
            };

            // 检查金额字段是否为空
            const amountField = document.getElementById('amount');
            const amountValue = amountField.value;
            if (!amountValue || amountValue.trim() === '') {
                // 如果金额为空，弹出自定义确认框
                const confirmed = await showCustomConfirm('您未填写申请/采购金额，点击<span class="text-blue-600 font-bold">确认</span>则直接提交申请，点击<span class="text-gray-800 font-bold">返回填写</span>完成金额填写', amountField);
                if (!confirmed) {
                    // 用户点击"返回填写"，恢复按钮状态并不继续提交
                    restoreButtonState();
                    return;
                }
                // 用户点击"确认"，继续提交
            }

            const formData = new FormData();
            const files = document.getElementById('attachments').files;
            for (let i = 0; i < files.length; i++) {
                formData.append('attachments', files[i]);
            }

            try {
                const uploadResponse = await fetch('/upload', {
                    method: 'POST',
                    body: formData
                });
                const uploadResult = await uploadResponse.json();
                if (uploadResult.success) {
                    // 获取选中的厂长
                    const selectedDirectors = getSelectedDirectors(document.getElementById('directorsList'));

                    const application = {
                        applicant: currentUser, // 使用当前用户作为申请人
                        department: document.getElementById('department').value,
                        date: document.getElementById('applyDate').value,
                        content: document.getElementById('content').value,
                        amount: document.getElementById('amount').value || null,
                        currency: document.getElementById('currency').value,
                        priority: document.getElementById('priority').value,
                        attachments: uploadResult.files,
                        username: currentUser,
                        selectedDirectors: selectedDirectors
                    };

                    const submitResponse = await fetch('/submit', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(application)
                    });
                    const submitResult = await submitResponse.json();
                    if (submitResult.success) {
                        // 恢复按钮状态
                        restoreButtonState();

                        resetForm();
                        alert('申请提交成功！');

                        // 更新本地应用数据
                        if (submitResult.application) {
                            // 将新应用添加到本地数据
                            applications.push(submitResult.application);

                            // 使用服务器返回的应用数据生成申请书模板
                            generateApplicationTemplate(submitResult.application);
                        } else {
                            // 如果服务器没有返回应用数据，则重新加载所有应用
                            await loadApplications();
                        }
                    } else {
                        // 恢复按钮状态
                        restoreButtonState();
                        alert('提交失败：' + submitResult.message);
                    }
                } else {
                    // 恢复按钮状态
                    restoreButtonState();
                    alert('文件上传失败：' + uploadResult.message);
                }
            } catch (error) {
                // 恢复按钮状态
                restoreButtonState();
                console.error('提交失败:', error);
                alert('提交失败：' + error.message);
            }
        });

        document.getElementById('editForm').addEventListener('submit', async (e) => {
            e.preventDefault();

            // 验证编辑表单的数据
            const editValidationResult = validateEditForm();
            if (!editValidationResult.isValid) {
                // 显示验证错误信息
                showValidationError(editValidationResult.errors);
                return;
            }

            const formData = new FormData();
            const files = document.getElementById('editAttachments').files;

            // 显示上传中提示
            const editFileList = document.getElementById('editFileList');
            if (files.length > 0) {
                editFileList.innerHTML += '<div class="mt-2 text-blue-600">附件上传中，请稍候...</div>';
            }

            for (let i = 0; i < files.length; i++) {
                formData.append('attachments', files[i]);
            }
            formData.append('id', document.getElementById('editId').value);
            formData.append('username', currentUser);
            formData.append('role', currentRole);
            formData.append('applicant', document.getElementById('editApplicant').value);
            formData.append('department', document.getElementById('editApplicationDepartment').value);
            formData.append('date', document.getElementById('editDate').value);
            formData.append('content', document.getElementById('editContent').value);
            formData.append('amount', document.getElementById('editAmount').value || '');
            formData.append('currency', document.getElementById('editCurrency').value);
            formData.append('priority', document.getElementById('editPriority').value);

            // 获取选中的厂长
            const selectedDirectors = getSelectedDirectors(document.getElementById('editDirectorsList'));
            formData.append('selectedDirectors', JSON.stringify(selectedDirectors));

            try {
                const response = await fetch('/modify', {
                    method: 'POST',
                    body: formData
                });
                const result = await response.json();
                if (result.success) {
                    closeEditModal();
                    alert('申请修改成功');

                    // 更新本地应用数据
                    const appId = parseInt(document.getElementById('editId').value);
                    const appIndex = applications.findIndex(a => a.id === appId);
                    if (appIndex !== -1 && result.application) {
                        // 使用服务器返回的更新后的应用数据
                        applications[appIndex] = result.application;

                        // 更新当前视图
                        updateCurrentView();
                    } else {
                        // 如果服务器没有返回更新后的应用数据，则重新加载所有应用
                        await loadApplications();

                        // 更新当前视图
                        updateCurrentView();
                    }
                } else {
                    alert('修改失败：' + result.message);
                }
            } catch (error) {
                console.error('修改失败:', error);
                alert('修改失败');
            }
        });

        // 严格验证申请表单的所有必填字段
        function validateApplicationForm() {
            const errors = [];

            // 验证申请人姓名
            const applicant = document.getElementById('applicant').value;
            if (!applicant || applicant.trim() === '') {
                errors.push('申请人姓名不能为空');
            } else if (applicant.trim().length < 2) {
                errors.push('申请人姓名至少需要2个字符');
            }

            // 验证申请部门
            const department = document.getElementById('department').value;
            if (!department || department.trim() === '') {
                errors.push('申请部门不能为空');
            }

            // 验证申请日期
            const date = document.getElementById('applyDate').value;
            if (!date || date.trim() === '') {
                errors.push('申请日期不能为空');
            } else {
                // 验证日期格式和合理性
                const selectedDate = new Date(date);
                const today = new Date();
                const oneYearAgo = new Date();
                oneYearAgo.setFullYear(today.getFullYear() - 1);
                const oneYearLater = new Date();
                oneYearLater.setFullYear(today.getFullYear() + 1);

                if (isNaN(selectedDate.getTime())) {
                    errors.push('申请日期格式无效');
                } else if (selectedDate < oneYearAgo) {
                    errors.push('申请日期不能早于一年前');
                } else if (selectedDate > oneYearLater) {
                    errors.push('申请日期不能晚于一年后');
                }
            }

            // 验证申请内容
            const content = document.getElementById('content').value;
            if (!content || content.trim() === '') {
                errors.push('申请内容不能为空');
            } else if (content.trim().length < 10) {
                errors.push('申请内容至少需要10个字符');
            } else if (content.trim().length > 2000) {
                errors.push('申请内容不能超过2000个字符');
            }

            // 验证金额（如果填写了）
            const amount = document.getElementById('amount').value;
            if (amount && amount.trim() !== '') {
                const amountNum = parseFloat(amount);
                if (isNaN(amountNum)) {
                    errors.push('申请金额必须是有效数字');
                } else if (amountNum < 0) {
                    errors.push('申请金额不能为负数');
                } else if (amountNum > 99999999) {
                    errors.push('申请金额不能超过99,999,999');
                }
            }

            // 验证优先级
            const priority = document.getElementById('priority').value;
            if (!priority || priority.trim() === '') {
                errors.push('请选择申请优先级');
            }

            // 验证厂长选择
            const selectedDirectors = getSelectedDirectors(document.getElementById('directorsList'));
            if (!selectedDirectors || selectedDirectors.length === 0) {
                errors.push('请至少选择一位厂长进行审批');
            }

            // 币种字段有默认值，不需要验证

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        // 验证编辑表单的所有必填字段
        function validateEditForm() {
            const errors = [];

            // 验证申请人姓名
            const applicant = document.getElementById('editApplicant').value;
            if (!applicant || applicant.trim() === '') {
                errors.push('申请人姓名不能为空');
            } else if (applicant.trim().length < 2) {
                errors.push('申请人姓名至少需要2个字符');
            }

            // 验证申请部门
            const department = document.getElementById('editApplicationDepartment').value;
            if (!department || department.trim() === '') {
                errors.push('申请部门不能为空');
            }

            // 验证申请日期
            const date = document.getElementById('editDate').value;
            if (!date || date.trim() === '') {
                errors.push('申请日期不能为空');
            } else {
                // 验证日期格式和合理性
                const selectedDate = new Date(date);
                const today = new Date();
                const oneYearAgo = new Date();
                oneYearAgo.setFullYear(today.getFullYear() - 1);
                const oneYearLater = new Date();
                oneYearLater.setFullYear(today.getFullYear() + 1);

                if (isNaN(selectedDate.getTime())) {
                    errors.push('申请日期格式无效');
                } else if (selectedDate < oneYearAgo) {
                    errors.push('申请日期不能早于一年前');
                } else if (selectedDate > oneYearLater) {
                    errors.push('申请日期不能晚于一年后');
                }
            }

            // 验证申请内容
            const content = document.getElementById('editContent').value;
            if (!content || content.trim() === '') {
                errors.push('申请内容不能为空');
            } else if (content.trim().length < 10) {
                errors.push('申请内容至少需要10个字符');
            } else if (content.trim().length > 2000) {
                errors.push('申请内容不能超过2000个字符');
            }

            // 验证金额（如果填写了）
            const amount = document.getElementById('editAmount').value;
            if (amount && amount.trim() !== '') {
                const amountNum = parseFloat(amount);
                if (isNaN(amountNum)) {
                    errors.push('申请金额必须是有效数字');
                } else if (amountNum < 0) {
                    errors.push('申请金额不能为负数');
                } else if (amountNum > 99999999) {
                    errors.push('申请金额不能超过99,999,999');
                }
            }

            // 验证优先级
            const priority = document.getElementById('editPriority').value;
            if (!priority || priority.trim() === '') {
                errors.push('请选择申请优先级');
            }

            // 币种字段有默认值，不需要验证

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        // 验证添加用户表单的所有必填字段
        function validateAddUserForm(username, password, role, email, userCode) {
            const errors = [];

            // 验证用户名
            if (!username || username.trim() === '') {
                errors.push('用户名不能为空');
            } else if (username.trim().length < 3) {
                errors.push('用户名至少需要3个字符');
            } else if (username.trim().length > 20) {
                errors.push('用户名不能超过20个字符');
            }

            // 验证密码 - 只验证密码至少6位数
            if (!password || password.trim() === '') {
                errors.push('密码不能为空');
            } else if (password.length < 6) {
                errors.push('密码至少需要6个字符');
            } else if (password.length > 50) {
                errors.push('密码不能超过50个字符');
            }

            // 验证角色
            if (!role || role.trim() === '') {
                errors.push('请选择用户角色');
            } else {
                const validRoles = ['user', 'director', 'chief', 'manager', 'ceo', 'admin', 'readonly'];
                if (!validRoles.includes(role)) {
                    errors.push('用户角色值无效');
                }
            }

            // 验证邮箱（如果填写了）
            if (email && email.trim() !== '') {
                const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                if (!emailRegex.test(email.trim())) {
                    errors.push('邮箱地址格式无效');
                }
            }

            // 验证用户编号（如果填写了）
            if (userCode && userCode.trim() !== '') {
                if (userCode.trim().length > 20) {
                    errors.push('用户编号不能超过20个字符');
                }
                if (!/^[A-Za-z0-9]{6}$/.test(userCode.trim())) {
                    errors.push('用户代码必须是6位字母或数字组合');
                }
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        // 验证预览申请书模板的数据
        function validatePreviewData(applicant, department, date, content) {
            const errors = [];

            // 验证申请人姓名
            if (!applicant || applicant.trim() === '') {
                errors.push('申请人姓名不能为空');
            } else if (applicant.trim().length < 2) {
                errors.push('申请人姓名至少需要2个字符');
            }

            // 验证申请部门
            if (!department || department.trim() === '') {
                errors.push('申请部门不能为空');
            }

            // 验证申请日期
            if (!date || date.trim() === '') {
                errors.push('申请日期不能为空');
            } else {
                // 验证日期格式和合理性
                const selectedDate = new Date(date);
                const today = new Date();
                const oneYearAgo = new Date();
                oneYearAgo.setFullYear(today.getFullYear() - 1);
                const oneYearLater = new Date();
                oneYearLater.setFullYear(today.getFullYear() + 1);

                if (isNaN(selectedDate.getTime())) {
                    errors.push('申请日期格式无效');
                } else if (selectedDate < oneYearAgo) {
                    errors.push('申请日期不能早于一年前');
                } else if (selectedDate > oneYearLater) {
                    errors.push('申请日期不能晚于一年后');
                }
            }

            // 验证申请内容
            if (!content || content.trim() === '') {
                errors.push('申请内容不能为空');
            } else if (content.trim().length < 10) {
                errors.push('申请内容至少需要10个字符');
            } else if (content.trim().length > 2000) {
                errors.push('申请内容不能超过2000个字符');
            }

            return {
                isValid: errors.length === 0,
                errors: errors
            };
        }

        // 显示验证错误信息
        function showValidationError(errors) {
            const errorMessage = '请修正以下问题后再提交：\n\n' + errors.map((error, index) => `${index + 1}. ${error}`).join('\n');

            // 移动端优化的错误提示
            if (window.innerWidth <= 768) {
                showMobileAlert(errorMessage);
            } else {
                alert(errorMessage);
            }
        }

        function resetForm() {
            document.getElementById('applicationForm').reset();
            document.getElementById('attachments').value = '';
            updateFileList([]);

            // 重置厂长选择列表
            renderDirectorsList(document.getElementById('directorsList'), []);

            // 重新设置申请人姓名为当前用户
            document.getElementById('applicant').value = currentUser;

            // 重新设置申请部门为当前用户的部门
            if (currentDepartment) {
                const departmentInput = document.getElementById('department');
                if (departmentInput) {
                    departmentInput.value = currentDepartment;
                }
            }
        }

        // 加载应用数据 - 优化版，提升加载速度
        async function loadApplications(updateView = true, showError = true) {
            try {
                // 显示加载状态（仅在首次加载时）
                if (updateView && showError) {
                    showCenterLoadingIndicator('正在加载应用数据...');
                }

                // 使用优化的API请求工具
                applications = await apiRequest('/applications', {
                    method: 'GET',
                    timeout: 8000, // 优化超时时间
                    retry: {
                        maxRetries: 2, // 减少重试次数
                        retryDelay: 500, // 减少重试延迟
                        retryStatusCodes: [0, 408, 429, 500, 502, 503, 504]
                    }
                });

                // 验证数据格式
                if (!Array.isArray(applications)) {
                    throw new Error('服务器返回的数据格式不正确');
                }

                // 提取所有用户的电子签名信息
                extractSignaturesFromApplications();

                // 更新待审核数量
                updatePendingCount();

                // 更新视图
                if (updateView) {
                    // 获取当前活动的页面
                    const activeSection = document.querySelector('.side-nav-btn.active-indicator');
                    if (activeSection) {
                        const section = activeSection.dataset.section;
                        if (section === 'history') {
                            updateHistoryList();
                        } else if (section === 'pendingApproval') {
                            updatePendingApprovalList();
                        } else if (section === 'approved') {
                            updateApprovedList();
                        } else if (section === 'new') {
                            // 确保新建申请表单中的申请人姓名和部门始终为当前用户
                            document.getElementById('applicant').value = currentUser;
                            if (currentDepartment) {
                                const departmentInput = document.getElementById('department');
                                if (departmentInput) {
                                    departmentInput.value = currentDepartment;
                                }
                            }
                        }
                    }
                }

                // 隐藏加载状态
                if (updateView && showError) {
                    hideCenterLoadingIndicator();
                }

                console.log(`成功加载 ${applications.length} 个应用数据`);
                return applications;
            } catch (error) {
                console.error('加载应用数据失败:', error);

                // 隐藏加载状态
                if (updateView && showError) {
                    hideCenterLoadingIndicator();
                }

                // 根据错误类型提供不同的处理
                if (error.status === 'TIMEOUT') {
                    if (showError) {
                        showNetworkStatus('网络连接超时，正在重试...', 'error');
                        // 自动重试一次
                        setTimeout(() => {
                            loadApplications(updateView, false);
                        }, 3000);
                    }
                } else if (error.message && error.message.includes('network')) {
                    if (showError) {
                        showNetworkStatus('网络连接异常，请检查网络设置', 'error');
                    }
                } else {
                    if (showError) {
                        showNetworkStatus('加载应用数据失败，请稍后重试', 'error');
                    }
                }

                // 返回空数组以避免后续错误
                return [];
            }
        }

        // 存储原始用户列表和搜索结果
        let originalUsers = [];
        let filteredUsers = [];
        let isSearchActive = false;

        async function loadUsers() {
            if (currentRole !== 'admin') return;
            try {
                // 使用增强的API请求工具
                originalUsers = await apiRequest(`/users?username=${currentUser}`, {
                    method: 'GET',
                    timeout: 15000,
                    retry: {
                        maxRetries: 3,
                        retryDelay: 1500,
                        retryStatusCodes: [0, 408, 429, 500, 502, 503, 504]
                    }
                });

                // 验证数据格式
                if (!Array.isArray(originalUsers)) {
                    throw new Error('服务器返回的用户数据格式不正确');
                }

                // 获取用户列表并按用户名自然排序
                originalUsers.sort((a, b) => a.username.localeCompare(b.username, undefined, { sensitivity: 'base', numeric: true }));
                allUsers = [...originalUsers]; // 复制一份给allUsers
                currentUserPage = 1; // 重置为第一页
                renderUsersList();
            } catch (error) {
                console.error('加载用户失败:', error);
                showNetworkStatus('加载用户数据失败，请稍后重试', 'error');
            }
        }

        // 渲染用户列表（带分页）
        function renderUsersList() {
            const startIndex = (currentUserPage - 1) * usersPerPage;
            const endIndex = startIndex + usersPerPage;
            const usersToShow = allUsers.slice(startIndex, endIndex);

            const tbody = document.getElementById('usersList');
            tbody.innerHTML = usersToShow.map(user => `
                <tr>
                    <td class="px-6 py-4">${sanitizeInput(user.username)}</td>
                    <td class="px-6 py-4">${sanitizeInput(user.userCode || '')}</td>
                    <td class="px-6 py-4">${roleMap[user.role] || user.role}</td>
                    <td class="px-6 py-4">${user.role === 'chief' ? '经理室' : (user.role === 'manager' || user.role === 'admin' ? '不适用' : (user.department || '未设置'))}</td>
                    <td class="px-6 py-4">
                        ${user.signature ?
                            `<img src="${user.signature}" alt="电子签名" class="max-h-10">` :
                            '<span class="text-gray-400">未设置</span>'}
                    </td>
                    <td class="px-6 py-4">
                        ${deleteMode ?
                            // 删除模式下显示删除按钮
                            (user.username !== currentUser ?
                                `<button onclick="deleteUser('${user.username}')" class="delete-user-btn">×</button>` :
                                '<span class="text-gray-400">当前用户</span>'
                            ) :
                            // 正常模式下显示编辑按钮
                            `<button onclick="showEditUserModal('${user.username}')" class="edit-user-btn">编辑</button>`
                        }
                    </td>
                </tr>
            `).join('');

            // 更新分页信息
            const totalPages = Math.ceil(allUsers.length / usersPerPage);
            document.getElementById('currentPage').textContent = currentUserPage;
            document.getElementById('totalPages').textContent = totalPages;

            // 更新按钮状态
            document.getElementById('prevPageBtn').disabled = currentUserPage === 1;
            document.getElementById('nextPageBtn').disabled = currentUserPage === totalPages;

            // 更新搜索状态提示
            const searchInput = document.getElementById('userSearchInput');
            if (isSearchActive && searchInput.value.trim() !== '') {
                // 如果没有搜索结果，显示提示
                if (allUsers.length === 0) {
                    tbody.innerHTML = `<tr><td colspan="6" class="px-6 py-4 text-center text-gray-500">没有找到匹配的用户</td></tr>`;
                }
            }
        }

        // 搜索用户函数
        function searchUsers() {
            const searchTerm = document.getElementById('userSearchInput').value.trim().toLowerCase();
            if (searchTerm === '') {
                resetUserSearch();
                return;
            }

            isSearchActive = true;
            allUsers = originalUsers.filter(user => {
                const username = (user.username || '').toLowerCase();
                const userCode = (user.userCode || '').toLowerCase();
                const role = (roleMap[user.role] || user.role || '').toLowerCase();
                const department = (user.department || '').toLowerCase();

                return username.includes(searchTerm) ||
                       userCode.includes(searchTerm) ||
                       role.includes(searchTerm) ||
                       department.includes(searchTerm);
            });

            currentUserPage = 1; // 重置为第一页
            renderUsersList();
        }

        // 重置搜索函数
        function resetUserSearch() {
            document.getElementById('userSearchInput').value = '';
            isSearchActive = false;
            allUsers = [...originalUsers]; // 恢复原始用户列表
            currentUserPage = 1; // 重置为第一页
            renderUsersList();
        }

        // 根据角色切换部门选择框的显示/隐藏
        function toggleDepartmentSelect(username) {
            const roleSelect = document.getElementById(`role-${username}`);
            const departmentSelect = document.getElementById(`department-${username}`);
            if (['chief', 'manager', 'admin'].includes(roleSelect.value)) {
                departmentSelect.classList.add('hidden');
            } else {
                departmentSelect.classList.remove('hidden');
            }
        }

        // 显示添加用户模态框
        function showAddUserModal() {
            // 重置表单
            document.getElementById('addUserForm').reset();
            document.getElementById('signaturePreview').classList.add('hidden');

            // 处理部门字段显示/隐藏
            toggleNewUserDepartment();

            // 显示模态框
            document.getElementById('addUserModal').classList.remove('hidden');

            // 应用模态框高度控制
            if (typeof adjustModalHeight === 'function') {
                setTimeout(() => adjustModalHeight('addUserModal'), 10);
            }
        }

        // 关闭添加用户模态框
        function closeAddUserModal() {
            document.getElementById('addUserModal').classList.add('hidden');
            // 重置表单
            document.getElementById('addUserForm').reset();
            document.getElementById('signaturePreview').classList.add('hidden');
        }

        // 切换新用户部门字段显示/隐藏
        function toggleNewUserDepartment() {
            const role = document.getElementById('newUserRole').value;
            const departmentField = document.getElementById('newUserDepartmentField');
            if (['manager', 'admin', 'chief', 'ceo'].includes(role)) {
                departmentField.classList.add('hidden');
            } else {
                departmentField.classList.remove('hidden');
            }
        }

        // 显示编辑用户模态框
        function showEditUserModal(username) {
            const user = allUsers.find(u => u.username === username);
            if (!user) return;

            // 填充表单
            document.getElementById('editTargetUsername').value = username;
            document.getElementById('editUserCode').value = user.userCode || '';
            document.getElementById('editRole').value = user.role || 'user';
            document.getElementById('editUserDepartment').value = user.department || '生产部';
            document.getElementById('editEmail').value = user.email || '';
            document.getElementById('editPassword').value = '';

            // 处理部门字段显示/隐藏
            toggleEditDepartment();

            // 显示签名预览
            const signaturePreview = document.getElementById('editSignaturePreview');
            const signatureImage = document.getElementById('editSignatureImage');
            if (user.signature) {
                signatureImage.src = user.signature;
                signaturePreview.classList.remove('hidden');
            } else {
                signaturePreview.classList.add('hidden');
            }

            // 显示模态框
            document.getElementById('editUserModal').classList.remove('hidden');
        }

        // 关闭编辑用户模态框
        function closeEditUserModal() {
            document.getElementById('editUserModal').classList.add('hidden');
        }

        // 切换编辑模态框中的部门选择框显示/隐藏
        function toggleEditDepartment() {
            const role = document.getElementById('editRole').value;
            const departmentField = document.getElementById('editDepartmentField');

            if (['chief', 'manager', 'admin', 'ceo'].includes(role)) {
                departmentField.classList.add('hidden');
            } else {
                departmentField.classList.remove('hidden');
            }
        }

        // 处理编辑用户表单提交
        async function handleEditUserSubmit(e) {
            e.preventDefault();

            const targetUsername = document.getElementById('editTargetUsername').value;
            const newRole = document.getElementById('editRole').value;
            const email = document.getElementById('editEmail').value;
            const userCode = document.getElementById('editUserCode').value || '';
            const signatureFile = document.getElementById('editSignature').files[0];
            const newPassword = document.getElementById('editPassword').value;

            // 根据角色确定部门
            let department = '';
            if (newRole === 'chief') {
                department = '经理室';
            } else if (newRole === 'manager' || newRole === 'admin' || newRole === 'ceo') {
                department = '';
            } else {
                department = document.getElementById('editUserDepartment').value;
            }

            try {
                // 显示加载指示器
                showLoadingIndicator('正在更新用户数据...');

                // 如果有新的签名文件，先转换为base64
                let signatureData = null;
                if (signatureFile) {
                    signatureData = await readFileAsDataURL(signatureFile);
                }

                const response = await fetch('/updateUser', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        adminUsername: currentUser,
                        targetUsername,
                        newRole,
                        email,
                        department,
                        userCode,
                        signature: signatureData,
                        newPassword
                    })
                });

                const result = await response.json();

                // 隐藏加载指示器
                hideLoadingIndicator();

                if (result.success) {
                    alert('用户更新成功');
                    // 保存当前页码
                    const currentPageBeforeReload = currentUserPage;
                    // 重新加载用户列表
                    await loadUsers();
                    // 恢复到之前的页码
                    currentUserPage = currentPageBeforeReload;
                    // 确保页码不超过总页数
                    const totalPages = Math.ceil(allUsers.length / usersPerPage);
                    if (currentUserPage > totalPages) {
                        currentUserPage = totalPages;
                    }
                    renderUsersList();
                    // 关闭模态框
                    closeEditUserModal();
                } else {
                    alert('用户更新失败：' + result.message);
                }
            } catch (error) {
                console.error('更新用户失败:', error);
                hideLoadingIndicator();
                alert('更新用户失败: ' + (error.message || '未知错误'));
            }
        }

        // 显示加载指示器
        function showLoadingIndicator(message) {
            // 检查是否已存在加载指示器
            if (document.getElementById('loadingIndicator')) {
                return;
            }

            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'loadingIndicator';
            loadingIndicator.className = 'loading-overlay';
            loadingIndicator.innerHTML = `
                <div class="loading-container">
                    <div class="spinner mb-2"></div>
                    <p>${message || '正在加载...'}</p>
                </div>
            `;
            document.body.appendChild(loadingIndicator);
        }

        // 隐藏加载指示器
        function hideLoadingIndicator() {
            const loadingIndicator = document.getElementById('loadingIndicator');
            if (loadingIndicator) {
                document.body.removeChild(loadingIndicator);
            }
        }

        // 显示居中加载指示器 - 专门用于应用数据加载
        function showCenterLoadingIndicator(message) {
            // 检查是否已存在居中加载指示器
            if (document.getElementById('centerLoadingIndicator')) {
                return;
            }

            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'centerLoadingIndicator';
            loadingIndicator.className = 'fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50';
            loadingIndicator.innerHTML = `
                <div class="bg-white p-6 rounded-lg shadow-lg border border-gray-200">
                    <div class="text-center">
                        <div class="relative mb-4">
                            <div class="inline-block animate-spin rounded-full h-12 w-12 border-4 border-blue-200 border-t-blue-600"></div>
                            <div class="absolute inset-0 flex items-center justify-center">
                                <svg class="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                                </svg>
                            </div>
                        </div>
                        <h3 class="text-lg font-semibold text-gray-900 mb-2">${message || '正在加载...'}</h3>
                        <p class="text-gray-600 text-sm">请稍候，系统正在为您加载数据</p>
                    </div>
                </div>
            `;
            document.body.appendChild(loadingIndicator);
        }

        // 隐藏居中加载指示器
        function hideCenterLoadingIndicator() {
            const loadingIndicator = document.getElementById('centerLoadingIndicator');
            if (loadingIndicator) {
                document.body.removeChild(loadingIndicator);
            }
        }

        // 显示网络状态 - 支持代理和VPN网络的状态提示
        function showNetworkStatus(message, type = 'info') {
            // 移除现有的状态提示
            hideNetworkStatus();

            const statusElement = document.createElement('div');
            statusElement.id = 'networkStatus';
            statusElement.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 max-w-sm ${
                type === 'loading' ? 'bg-blue-500 text-white' :
                type === 'error' ? 'bg-red-500 text-white' :
                type === 'success' ? 'bg-green-500 text-white' :
                'bg-gray-500 text-white'
            }`;

            statusElement.innerHTML = `
                <div class="flex items-center">
                    ${type === 'loading' ?
                        '<div class="spinner mr-3"></div>' :
                        type === 'error' ?
                        '<span class="mr-3">⚠️</span>' :
                        type === 'success' ?
                        '<span class="mr-3">✅</span>' :
                        '<span class="mr-3">ℹ️</span>'
                    }
                    <span>${message}</span>
                </div>
            `;

            document.body.appendChild(statusElement);

            // 自动隐藏（除了加载状态）
            if (type !== 'loading') {
                setTimeout(() => {
                    hideNetworkStatus();
                }, 5000);
            }
        }

        // 隐藏网络状态
        function hideNetworkStatus() {
            const statusElement = document.getElementById('networkStatus');
            if (statusElement) {
                document.body.removeChild(statusElement);
            }
        }

        // 专门的网络工具函数 - 为代理和VPN网络优化
        async function makeNetworkRequest(url, options = {}) {
            const defaultOptions = {
                method: 'GET',
                timeout: 15000,
                retry: {
                    maxRetries: 3,
                    retryDelay: 1500,
                    retryStatusCodes: [0, 408, 429, 500, 502, 503, 504, 520, 521, 522, 523, 524]
                }
            };

            const mergedOptions = {
                ...defaultOptions,
                ...options,
                retry: {
                    ...defaultOptions.retry,
                    ...options.retry
                }
            };

            try {
                return await apiRequest(url, mergedOptions);
            } catch (error) {
                console.error(`网络请求失败 ${url}:`, error);

                // 如果是网络相关错误，触发网络诊断
                if (error.status === 'TIMEOUT' ||
                    (error.message && (error.message.includes('network') ||
                                      error.message.includes('Failed to fetch')))) {

                    // 运行网络诊断
                    if (window.networkDiagnostics) {
                        const diagnostics = await window.networkDiagnostics.runDiagnostics();
                        console.log('网络诊断结果:', diagnostics);

                        if (diagnostics.quality.status === 'failed') {
                            showNetworkStatus('网络连接异常，请检查网络设置或代理配置', 'error');
                        }
                    }
                }

                throw error;
            }
        }

        // 将文件读取为DataURL（base64格式）
        function readFileAsDataURL(file) {
            return new Promise((resolve, reject) => {
                const reader = new FileReader();
                reader.onload = () => resolve(reader.result);
                reader.onerror = () => reject(new Error('文件读取失败'));
                reader.readAsDataURL(file);
            });
        }

        async function updateUser(targetUsername) {
            const newRole = document.getElementById(`role-${targetUsername}`).value;
            const email = document.getElementById(`email-${targetUsername}`).value;
            const userCode = document.getElementById(`userCode-${targetUsername}`).value || '';
            // 如果是总监，设置部门为"经理室"，如果是其他管理员、经理或CEO则为空，其他角色使用选择的部门
            let department = '';
            if (newRole === 'chief') {
                department = '经理室';
            } else if (newRole === 'manager' || newRole === 'admin' || newRole === 'ceo') {
                department = '';
            } else {
                department = document.getElementById(`department-${targetUsername}`).value;
            }
            const signatureFile = document.getElementById(`signature-${targetUsername}`).files[0];
            const newPassword = document.getElementById(`password-${targetUsername}`).value;

            try {
                // 显示加载指示器
                const loadingIndicator = document.createElement('div');
                loadingIndicator.id = 'updateLoadingIndicator';
                loadingIndicator.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
                loadingIndicator.innerHTML = `
                    <div class="bg-white p-4 rounded-md shadow-lg">
                        <div class="spinner mb-2"></div>
                        <p>正在更新用户数据...</p>
                    </div>
                `;
                document.body.appendChild(loadingIndicator);

                // 如果有新的签名文件，先转换为base64
                let signatureData = null;
                if (signatureFile) {
                    console.log('处理签名文件:', signatureFile.name, signatureFile.type, signatureFile.size);
                    try {
                        // 检查文件大小
                        const maxSizeMB = 5; // 最大5MB
                        const maxSizeBytes = maxSizeMB * 1024 * 1024;

                        if (signatureFile.size > maxSizeBytes) {
                            throw new Error(`签名图片过大，请上传小于${maxSizeMB}MB的图片`);
                        }

                        // 检查文件类型
                        if (!signatureFile.type.startsWith('image/')) {
                            throw new Error('请上传图片格式的签名文件');
                        }

                        signatureData = await readFileAsDataURL(signatureFile);
                        console.log('签名数据长度:', signatureData.length);

                        // 验证签名数据格式
                        if (!signatureData.startsWith('data:image/')) {
                            throw new Error('签名文件格式无效，请使用图片文件');
                        }

                        // 如果签名数据过大，尝试压缩
                        if (signatureData.length > 5000000) { // 如果大于5MB
                            console.log('签名数据过大，尝试压缩...');
                            // 这里可以添加图片压缩逻辑，但目前先通过限制文件大小处理
                        }
                    } catch (fileError) {
                        document.body.removeChild(loadingIndicator);
                        alert('读取签名文件失败: ' + fileError.message);
                        return;
                    }
                }

                // 准备请求数据
                const requestData = {
                    adminUsername: currentUser,
                    targetUsername,
                    newRole,
                    email,
                    department,
                    userCode,
                    signature: signatureData,
                    newPassword
                };

                console.log('发送更新请求:', JSON.stringify({
                    ...requestData,
                    signature: signatureData ? `(签名数据长度: ${signatureData.length})` : null
                }));

                // 设置较长的超时时间
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), 30000); // 30秒超时

                try {
                    const response = await fetch('/updateUser', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify(requestData),
                        signal: controller.signal
                    });

                    clearTimeout(timeoutId); // 清除超时

                    if (!response.ok) {
                        throw new Error(`服务器响应错误: ${response.status} ${response.statusText}`);
                    }

                    const result = await response.json();

                    // 移除加载指示器
                    document.body.removeChild(loadingIndicator);

                    if (result.success) {
                        alert('用户更新成功');
                        // 保存当前页码
                        const currentPageBeforeReload = currentUserPage;
                        // 重新加载用户列表
                        await loadUsers();
                        // 由于loadUsers中已经进行了排序，这里不需要再次排序
                        // 恢复到之前的页码
                        currentUserPage = currentPageBeforeReload;
                        // 确保页码不超过总页数
                        const totalPages = Math.ceil(allUsers.length / usersPerPage);
                        if (currentUserPage > totalPages) {
                            currentUserPage = totalPages;
                        }
                        renderUsersList();
                    } else {
                        alert('用户更新失败：' + result.message);
                    }
                } catch (fetchError) {
                    clearTimeout(timeoutId); // 确保清除超时
                    throw fetchError; // 重新抛出以便外层捕获
                }
            } catch (error) {
                console.error('更新用户失败:', error);
                // 确保加载指示器被移除
                const loadingIndicator = document.getElementById('updateLoadingIndicator');
                if (loadingIndicator) {
                    document.body.removeChild(loadingIndicator);
                }
                alert('更新用户失败: ' + (error.message || '未知错误'));
            }
        }

        // 删除用户函数
        async function deleteUser(targetUsername) {
            if (!confirm(`确定要删除用户 ${targetUsername} 吗？此操作不可恢复！`)) {
                return;
            }

            try {
                const response = await fetch('/deleteUser', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ adminUsername: currentUser, targetUsername })
                });
                const result = await response.json();
                if (result.success) {
                    alert('用户删除成功');
                    // 保存当前页码
                    let currentPageBeforeReload = currentUserPage;
                    // 重新加载用户列表
                    await loadUsers();
                    // 确保页码不超过总页数
                    const totalPages = Math.ceil(allUsers.length / usersPerPage);
                    if (currentPageBeforeReload > totalPages) {
                        currentPageBeforeReload = totalPages;
                    }
                    currentUserPage = currentPageBeforeReload > 0 ? currentPageBeforeReload : 1;
                    renderUsersList();
                } else {
                    alert('用户删除失败：' + result.message);
                }
            } catch (error) {
                console.error('删除用户失败:', error);
                alert('删除用户失败');
            }
        }

        // 切换删除模式
        function toggleDeleteMode() {
            deleteMode = !deleteMode;
            const toggleBtn = document.getElementById('toggleDeleteModeBtn');
            const deleteAlert = document.getElementById('deleteModeAlert');

            if (deleteMode) {
                toggleBtn.innerHTML = `
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                    </svg>
                    取消删除
                `;
                toggleBtn.classList.remove('bg-red-600', 'hover:bg-red-700');
                toggleBtn.classList.add('bg-gray-600', 'hover:bg-gray-700');
                deleteAlert.classList.remove('hidden');
            } else {
                toggleBtn.innerHTML = `
                    <svg class="w-4 h-4 inline mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"></path>
                    </svg>
                    删除用户
                `;
                toggleBtn.classList.remove('bg-gray-600', 'hover:bg-gray-700');
                toggleBtn.classList.add('bg-red-600', 'hover:bg-red-700');
                deleteAlert.classList.add('hidden');
            }

            // 重新渲染用户列表
            renderUsersList();
        }

        // 判断日期是否在指定范围内
        function isInRange(date, range) {
            const applyDate = new Date(date);
            const now = new Date();
            if (range === 'week') {
                const startOfWeek = new Date(now);
                startOfWeek.setDate(now.getDate() - now.getDay()); // 本周开始（周日）
                startOfWeek.setHours(0, 0, 0, 0);
                return applyDate >= startOfWeek && applyDate <= now;
            } else if (range === 'month') {
                const startOfMonth = new Date(now.getFullYear(), now.getMonth(), 1);
                return applyDate >= startOfMonth && applyDate <= now;
            } else if (range === 'year') {
                const startOfYear = new Date(now.getFullYear(), 0, 1);
                return applyDate >= startOfYear && applyDate <= now;
            }
            return true; // 'all'
        }

        function updateHistoryList(page = 1) {
            const range = document.getElementById('timeRange').value;
            const filteredApps = applications.filter(app => isInRange(app.date, range));
            const tbody = document.getElementById('applicationsList');

            // 根据用户角色筛选可见的申请
            let displayApps = [];

            if (currentRole === 'admin' || currentRole === 'readonly') {
                // 管理员和只读用户可以看到所有申请
                displayApps = filteredApps;
            } else {
                // 所有其他用户（包括普通用户、厂长、总监、经理）只能看到自己提交的申请
                displayApps = filteredApps.filter(app => app.username === currentUser);
            }

            // 历史记录按时间倒序排列（新的在前）
            displayApps = displayApps.sort((a, b) => new Date(b.date) - new Date(a.date));

            // 分页处理
            const pageSize = 10; // 每页显示10条记录
            const totalItems = displayApps.length;
            const totalPages = Math.ceil(totalItems / pageSize);

            // 确保当前页在有效范围内
            page = Math.max(1, Math.min(page, totalPages || 1));

            // 获取当前页的数据
            const startIndex = (page - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, totalItems);
            const currentPageData = displayApps.slice(startIndex, endIndex);

            // 渲染当前页数据
            renderApplicationsList(currentPageData, tbody);

            // 计算当前页金额合计
            if (tbody.id === 'applicationsList') {
                calculateTotalAmount(currentPageData);
            }

            // 更新分页控件
            updateHistoryPagination(page, totalPages, totalItems);
        }

        // 更新申请记录分页控件
        function updateHistoryPagination(currentPage, totalPages, totalItems) {
            const prevPageBtn = document.getElementById('historyPrevPage');
            const nextPageBtn = document.getElementById('historyNextPage');
            const pageNumbersContainer = document.getElementById('historyPageNumbers');
            const totalItemsSpan = document.getElementById('historyTotalItems');

            // 更新总记录数
            totalItemsSpan.textContent = totalItems;

            // 禁用/启用上一页、下一页按钮
            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= totalPages;

            // 设置按钮点击事件
            prevPageBtn.onclick = () => updateHistoryList(currentPage - 1);
            nextPageBtn.onclick = () => updateHistoryList(currentPage + 1);

            // 生成页码按钮
            pageNumbersContainer.innerHTML = '';

            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            // 调整起始页，确保始终显示5个页码（如果有足够的页数）
            if (endPage - startPage < 4 && totalPages > 5) {
                startPage = Math.max(1, endPage - 4);
            }

            // 添加第一页按钮（如果不在显示范围内）
            if (startPage > 1) {
                const firstPageBtn = document.createElement('button');
                firstPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
                firstPageBtn.textContent = '1';
                firstPageBtn.onclick = () => updateHistoryList(1);
                pageNumbersContainer.appendChild(firstPageBtn);

                // 添加省略号（如果第一页和起始页之间有间隔）
                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-2 py-1';
                    ellipsis.textContent = '...';
                    pageNumbersContainer.appendChild(ellipsis);
                }
            }

            // 添加页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `px-3 py-1 border rounded-md ${i === currentPage ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => updateHistoryList(i);
                pageNumbersContainer.appendChild(pageBtn);
            }

            // 添加最后一页按钮（如果不在显示范围内）
            if (endPage < totalPages) {
                // 添加省略号（如果结束页和最后一页之间有间隔）
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-2 py-1';
                    ellipsis.textContent = '...';
                    pageNumbersContainer.appendChild(ellipsis);
                }

                const lastPageBtn = document.createElement('button');
                lastPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
                lastPageBtn.textContent = totalPages;
                lastPageBtn.onclick = () => updateHistoryList(totalPages);
                pageNumbersContainer.appendChild(lastPageBtn);
            }
        }

        // 搜索申请
        function searchApplications() {
            const searchField = document.getElementById('searchField').value;
            const searchInput = document.getElementById('searchInput').value.toLowerCase();
            const range = document.getElementById('timeRange').value;

            if (!searchInput.trim()) {
                updateHistoryList(1); // 重置到第一页
                return;
            }

            const filteredApps = applications.filter(app => {
                // 先按时间范围筛选
                if (!isInRange(app.date, range)) return false;

                // 根据用户角色筛选可见的申请
                if (currentRole === 'admin' || currentRole === 'readonly') {
                    // 管理员和只读用户可以看到所有申请
                } else if (currentRole === 'director' || currentRole === 'chief' || currentRole === 'manager' || currentRole === 'ceo') {
                    // 审核人可以看到需要他们审核的申请
                    const needsApproval = needsApprovalByCurrentUser(app.status);
                    if (!needsApproval) {
                        // 如果不需要当前用户审核，则只显示自己提交的申请
                        if (app.username !== currentUser) return false;
                    } else {
                        // 如果需要当前用户审核，还需要检查是否指定了当前用户
                        if (currentRole === 'director') {
                            if (!(app.approvals && app.approvals.directors && app.approvals.directors[currentUser])) return false;
                        } else if (currentRole === 'manager') {
                            if (!(app.approvals && app.approvals.managers && app.approvals.managers[currentUser])) return false;
                        }
                        // CEO不需要额外检查，因为所有待CEO审批的申请都需要CEO审批
                    }
                } else {
                    // 普通用户只能看到自己的申请
                    if (app.username !== currentUser) return false;
                }

                // 按搜索字段筛选
                if (searchField === 'applicant') {
                    return app.applicant.toLowerCase().includes(searchInput);
                } else if (searchField === 'content') {
                    return app.content.toLowerCase().includes(searchInput);
                }
                return false;
            });

            // 按时间倒序排列
            const sortedApps = filteredApps.sort((a, b) => new Date(b.date) - new Date(a.date));

            // 分页处理
            const pageSize = 10;
            const totalItems = sortedApps.length;
            const totalPages = Math.ceil(totalItems / pageSize);
            const currentPage = 1; // 搜索结果从第一页开始显示

            // 获取当前页的数据
            const startIndex = 0;
            const endIndex = Math.min(pageSize, totalItems);
            const currentPageData = sortedApps.slice(startIndex, endIndex);

            // 渲染搜索结果
            const tbody = document.getElementById('applicationsList');
            renderApplicationsList(currentPageData, tbody);

            // 计算当前页金额合计
            calculateTotalAmount(currentPageData);

            // 更新分页控件
            updateHistoryPagination(currentPage, totalPages, totalItems);
        }

        // 渲染申请列表
        function renderApplicationsList(apps, tbodyId) {
            // 获取tbody元素
            const tbody = typeof tbodyId === 'string' ? document.getElementById(tbodyId) : tbodyId;

            if (!tbody) {
                console.error('找不到目标元素:', tbodyId);
                return;
            }

            if (apps.length === 0) {
                // 根据不同页面设置不同的colspan
                let colSpan = 9; // 默认值
                if (tbody.id === 'pendingApprovalList' || tbody.id === 'approvedList') {
                    colSpan = 8; // 待审核和已审核页面没有金额列
                }

                tbody.innerHTML = `<tr><td colspan="${colSpan}" class="px-6 py-4 text-center text-gray-500">暂无数据</td></tr>`;

                // 如果是申请记录页面，重置合计金额
                if (tbody.id === 'applicationsList') {
                    document.getElementById('currentPageTotalAmount').textContent = '0.00';
                }
                return;
            }

            // 判断当前是否在待审核页面
            const isPendingApprovalPage = tbody.id === 'pendingApprovalList';
            // 判断当前是否在申请记录页面
            const isHistoryPage = tbody.id === 'applicationsList';
            // 判断当前是否在已审核页面
            const isApprovedPage = tbody.id === 'approvedList';

            tbody.innerHTML = apps.map(app => {
                const isApplicant = app.username === currentUser;
                const isAdmin = currentRole === 'admin';
                const isReadOnly = currentRole === 'readonly';

                // 检查申请是否已经最终审核完成
                const isFinalStatus = app.status === '已通过' || app.status === '已拒绝';

                // 检查是否没有初始附件
                const hasNoInitialAttachments = !app.attachments || app.attachments.length === 0;

                // 申请人可以修改的条件：
                // 1. 申请处于待厂长审核状态，或
                // 2. 申请流程进行中（未最终审核完成）且一开始没有添加初始附件
                const canApplicantModify = isApplicant &&
                    (app.status === '待厂长审核' || (hasNoInitialAttachments && !isFinalStatus));

                // 申请人可以在特定条件下修改申请，管理员拥有所有操作权限，只读角色不能修改或删除
                const canEdit = (canApplicantModify || isAdmin) && !isReadOnly;
                // 删除权限仍然只限于待厂长审核状态
                const canDelete = ((isApplicant && app.status === '待厂长审核') || isAdmin) && !isReadOnly;

                let actions = [];

                // 在申请记录页面，申请人自己、管理员和只读角色可以查看详情
                if (isHistoryPage) {
                    if (isApplicant || isAdmin || isReadOnly) {
                        actions.push(`<button onclick="viewDetail(${app.id})" class="text-blue-600 hover:text-blue-800" data-id="${app.id}">查看</button>`);
                    }
                }
                // 在待审核页面，所有审核人都可以查看详情
                else if (isPendingApprovalPage) {
                    actions.push(`<button onclick="viewDetail(${app.id})" class="text-blue-600 hover:text-blue-800" data-id="${app.id}">审核申请</button>`);
                }
                // 在已审核页面，所有审核人都可以查看详情
                else if (isApprovedPage) {
                    actions.push(`<button onclick="viewDetail(${app.id})" class="text-blue-600 hover:text-blue-800" data-id="${app.id}">查看</button>`);
                }
                // 其他页面，所有人都可以查看详情
                else {
                    actions.push(`<button onclick="viewDetail(${app.id})" class="text-blue-600 hover:text-blue-800" data-id="${app.id}">查看</button>`);
                }

                if (canEdit) {
                    actions.push(`<button onclick="editApplication(${app.id})" class="text-green-600 hover:text-green-800">修改</button>`);
                }

                if (canDelete) {
                    actions.push(`<button onclick="deleteApplication(${app.id})" class="text-red-600 hover:text-red-800">删除</button>`);
                }

                // 只在待审核页面显示审批操作
                if (isPendingApprovalPage && canApprove(app)) {
                    // 移除审批下拉菜单，审批操作将在详情页面中进行
                    // actions.push(`
                    //     <select onchange="approveApplication(${app.id}, this.value)" class="border rounded p-1">
                    //         <option value="pending" selected>待审批</option>
                    //         <option value="approved">通过</option>
                    //         <option value="rejected">拒绝</option>
                    //     </select>
                    // `);
                }

                return `
                    <tr data-app-id="${app.id}">
                        <td class="px-6 py-4">${app.applicationCode || '无编号'}</td>
                        <td class="px-6 py-4">${sanitizeInput(app.applicant)}</td>
                        <td class="px-6 py-4">${sanitizeInput(app.department)}</td>
                        <td class="px-6 py-4">
                            ${app.date}
                            <span class="block text-xs text-gray-500">${formatSubmitTime(app.id)}</span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 rounded ${getPriorityClass(app.priority)}">${priorityMap[app.priority] || app.priority}</span>
                        </td>
                        <td class="px-6 py-4" style="max-width: 200px; width: 200px; white-space: normal; overflow: visible; word-break: break-word; vertical-align: top;">
                            ${createClickableContent(app.content)}
                        </td>
                        ${isHistoryPage ? `
                        <td class="px-6 py-4 text-right">
                            ${app.amount ? formatCurrency(parseFloat(app.amount), app.currency || 'CNY') : '无'}
                        </td>` : ''}
                        <td class="px-6 py-4">
                            ${app.status}
                            <span class="block text-xs text-gray-500">${getApplicationStatusDesc(app.status, app)}</span>
                            ${app.status === '待厂长审核' && app.approvals && app.approvals.directors ?
                                `<span class="block text-xs text-blue-600">待审核厂长：${Object.entries(app.approvals.directors)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([director, _]) => sanitizeInput(director))
                                    .join('、') || '无'}</span>`
                                : ''}
                            ${app.status === '待经理审批' && app.approvals && app.approvals.managers ?
                                `<span class="block text-xs text-blue-600">待审核经理：${Object.entries(app.approvals.managers)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([manager, _]) => sanitizeInput(manager))
                                    .join('、') || '无'}</span>`
                                : ''}
                        </td>
                        <td class="px-6 py-4 space-x-2">${actions.join(' ')}</td>
                    </tr>
                `;
            }).join('');
        }

        function canApprove(app) {
            // 只读角色不能进行审批操作
            if (currentRole === 'readonly') {
                return false;
            }

            // 管理员可以审批任何状态的申请
            if (currentRole === 'admin') {
                return true;
            }

            // 厂长审批
            if (currentRole === 'director' && app.status === '待厂长审核') {
                // 检查当前厂长是否是该申请的审批人
                return app.approvals.directors && app.approvals.directors[currentUser] &&
                       app.approvals.directors[currentUser].status === 'pending';
            }

            // 总监审批
            if (currentRole === 'chief' && app.status === '待总监审批') {
                return true;
            }

            // 经理审批
            if (currentRole === 'manager' && app.status === '待经理审批') {
                // 检查当前经理是否是该申请的审批人
                return app.approvals.managers && app.approvals.managers[currentUser] &&
                       app.approvals.managers[currentUser].status === 'pending';
            }

            // CEO审批
            if (currentRole === 'ceo' && app.status === '待CEO审批') {
                return true;
            }

            return false;
        }

        function getPriorityClass(priority) {
            switch (priority) {
                case 'high': return 'bg-red-100 text-red-800';
                case 'medium': return 'bg-yellow-100 text-yellow-800';
                default: return 'bg-green-100 text-green-800';
            }
        }

        function sanitizeInput(input) {
            const div = document.createElement('div');
            div.textContent = input;
            return div.innerHTML;
        }

        // 格式化提交时间，将时间戳转换为详细的时间格式
        function formatSubmitTime(timestamp) {
            if (!timestamp) return '';
            const date = new Date(parseInt(timestamp));
            if (isNaN(date.getTime())) return '';

            const hours = String(date.getHours()).padStart(2, '0');
            const minutes = String(date.getMinutes()).padStart(2, '0');
            const seconds = String(date.getSeconds()).padStart(2, '0');

            return `${hours}:${minutes}:${seconds}`;
        }

        async function deleteApplication(id) {
            if (!confirm('确定要删除此申请吗？')) return;
            try {
                const response = await fetch('/deleteApplication', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ id, username: currentUser, role: currentRole })
                });
                const result = await response.json();
                if (result.success) {
                    alert('申请删除成功');
                    loadApplications();
                } else {
                    alert('删除失败：' + result.message);
                }
            } catch (error) {
                console.error('删除失败:', error);
                alert('删除失败');
            }
        }

        function editApplication(id) {
            const app = applications.find(a => a.id === id);
            if (!app) return;

            document.getElementById('editId').value = app.id;
            document.getElementById('editApplicant').value = app.applicant;
            document.getElementById('editApplicationDepartment').value = app.department;
            document.getElementById('editDate').value = app.date;
            document.getElementById('editContent').value = app.content;
            document.getElementById('editAmount').value = app.amount || '';
            document.getElementById('editCurrency').value = app.currency || 'CNY';
            document.getElementById('editPriority').value = app.priority;
            document.getElementById('editAttachments').value = '';

            // 显示已有附件信息（只读展示，不可编辑）
            const editFileList = document.getElementById('editFileList');
            if (app.attachments && app.attachments.length > 0) {
                editFileList.innerHTML = `
                    <div class="p-2 bg-gray-100 rounded-md mb-2">
                        <p class="font-medium text-gray-700">已有附件（将被替换）：</p>
                        ${app.attachments.map(file => `
                            <div class="flex items-center justify-between p-1">
                                <span class="text-sm">${sanitizeInput(decodeURIComponent(file.name))}</span>
                            </div>
                        `).join('')}
                    </div>
                `;
            } else {
                editFileList.innerHTML = '';
            }

            // 加载已选择的厂长
            if (app.approvals && app.approvals.directors) {
                const selectedDirectors = Object.keys(app.approvals.directors);
                renderDirectorsList(document.getElementById('editDirectorsList'), selectedDirectors);
            } else {
                renderDirectorsList(document.getElementById('editDirectorsList'), []);
            }

            document.getElementById('editModal').classList.remove('hidden');
        }

        function closeEditModal() {
            document.getElementById('editModal').classList.add('hidden');
            // 重置滚动位置
            document.getElementById('editModal').scrollTop = 0;
        }

        async function approveApplication(id, status) {
            // 移除弹出确认对话框的部分，因为现在是在详情页面中进行审批操作
            // 保存当前卡片索引，用于判断是否需要自动显示下一个
            const wasCurrentCard = pendingCards.findIndex(app => app.id === id) === currentCardIndex;

            // 原有的审批逻辑
            const approvalSelect = document.querySelector(`select[onchange="approveApplication(${id}, this.value)"]`);
            if (approvalSelect) {
                approvalSelect.disabled = true;
            }

            // 显示处理中提示
            const msgElement = document.createElement('div');
            msgElement.id = 'processingMessage';
            msgElement.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
            msgElement.textContent = '处理中，请稍候...';
            document.body.appendChild(msgElement);

            try {
                // 获取当前申请
                const app = applications.find(a => a.id === id);
                if (!app) throw new Error('找不到申请');

                // 获取审批备注和附件
                let comment = '';
                let attachments = [];

                // 如果是从详情页面审批的，获取备注和附件
                const commentElement = document.getElementById(`approvalComment_${id}`);
                if (commentElement) {
                    comment = commentElement.value.trim();
                }

                // 修复：使用正确的文件输入元素ID获取审批附件
                const fileInput = document.getElementById('detail-new-attachments');
                if (fileInput && fileInput.files.length > 0) {
                    attachments = Array.from(fileInput.files);
                }

                // 如果是总监审批，还需要获取选择的经理
                let selectedManagers = [];
                if (currentRole === 'chief' && app.status === '待总监审批') {
                    const managerCheckboxes = document.querySelectorAll(`.manager-checkbox:checked`);
                    selectedManagers = Array.from(managerCheckboxes).map(cb => cb.value);

                    // 如果总监通过审批但没有选择经理，给出提醒
                    if (status === 'approved' && selectedManagers.length === 0) {
                        if (!confirm('您没有选择任何经理，申请将在您审批通过后直接流转给CEO进行最终审批。\n\n是否确认通过此申请？')) {
                            // 用户取消，移除处理中提示并停止审批流程
                            document.body.removeChild(msgElement);
                            return;
                        }
                    }
                }

                // 获取当前用户的电子签名
                const currentUserData = allUsers ? allUsers.find(u => u.username === currentUser) : null;
                const userSignature = currentUserData ? currentUserData.signature : null;

                // 调试信息：检查CEO签名获取
                if (currentRole === 'ceo') {
                    console.log('CEO审批签名调试信息:');
                    console.log('当前用户:', currentUser);
                    console.log('当前用户数据:', currentUserData);
                    console.log('用户签名:', userSignature ? '有签名' : '无签名');
                    if (userSignature) {
                        console.log('签名数据长度:', userSignature.length);
                        console.log('签名数据前50字符:', userSignature.substring(0, 50));
                    }
                }

                // 构建表单数据
                const formData = new FormData();
            formData.append('id', id);
            formData.append('status', status);
                formData.append('username', currentUser);
                formData.append('role', currentRole);
                formData.append('comment', comment);
                formData.append('signature', userSignature || '');

                // 添加附件
                for (let i = 0; i < attachments.length; i++) {
                    formData.append('newAttachments', attachments[i]);
                }

                // 如果是总监审批，添加选择的经理
                if (currentRole === 'chief' && app.status === '待总监审批' && selectedManagers.length > 0) {
                    formData.append('selectedManagers', JSON.stringify(selectedManagers));
                }

                // 发送审批请求
                const response = await fetch('/approve', {
                    method: 'POST',
                    body: formData
                });

                const result = await response.json();

                // 移除处理中提示
                document.body.removeChild(msgElement);

                if (result.success) {
                    // 更新本地应用数据
                    const appIndex = applications.findIndex(a => a.id === id);
                    if (appIndex !== -1) {
                        // 如果后端没有处理电子签名，在前端添加
                        if (!result.application.approvals) {
                            result.application.approvals = app.approvals || {};
                        }

                        // 根据角色更新审批信息
                        if (currentRole === 'director') {
                            if (!result.application.approvals.directors) {
                                result.application.approvals.directors = {};
                            }
                            if (!result.application.approvals.directors[currentUser]) {
                                result.application.approvals.directors[currentUser] = {};
                            }
                            result.application.approvals.directors[currentUser].status = status;
                            result.application.approvals.directors[currentUser].comment = comment;
                            result.application.approvals.directors[currentUser].date = new Date().toISOString();
                            result.application.approvals.directors[currentUser].signature = userSignature;
                        } else if (currentRole === 'chief') {
                            if (!result.application.approvals.chief) {
                                result.application.approvals.chief = {};
                            }
                            result.application.approvals.chief.status = status;
                            result.application.approvals.chief.comment = comment;
                            result.application.approvals.chief.date = new Date().toISOString();
                            result.application.approvals.chief.signature = userSignature;
                        } else if (currentRole === 'manager') {
                            if (!result.application.approvals.managers) {
                                result.application.approvals.managers = {};
                            }
                            if (!result.application.approvals.managers[currentUser]) {
                                result.application.approvals.managers[currentUser] = {};
                            }
                            result.application.approvals.managers[currentUser].status = status;
                            result.application.approvals.managers[currentUser].comment = comment;
                            result.application.approvals.managers[currentUser].date = new Date().toISOString();
                            result.application.approvals.managers[currentUser].signature = userSignature;
                        } else if (currentRole === 'ceo') {
                            if (!result.application.approvals.ceo) {
                                result.application.approvals.ceo = {};
                            }
                            result.application.approvals.ceo.status = status;
                            result.application.approvals.ceo.comment = comment;
                            result.application.approvals.ceo.date = new Date().toISOString();
                            result.application.approvals.ceo.signature = userSignature;
                            result.application.approvals.ceo.approverUsername = currentUser; // 记录审批人用户名，便于历史数据处理
                        }

                        applications[appIndex] = result.application;
                    }

                    // 显示成功消息
                    alert('审批成功');

                    // 强制关闭详情模态框
                    const detailModal = document.getElementById('detailModal');
                    if (detailModal) {
                        detailModal.classList.add('hidden');
                        // 重置滚动位置
                        detailModal.scrollTop = 0;
                    }

                    // 主动刷新待审核列表数据
                    await loadApplications();

                    // 如果当前在待审核或已审核页面，更新相应的列表
                    const activeSection = document.querySelector('.side-nav-btn.active')?.textContent;
                    if (activeSection && activeSection.includes('待审核')) {
                        // 更新待审核列表（后台数据）
                        updatePendingApprovalList();

                        // 如果是移动端卡片视图，立即处理下一个卡片
                        if (window.innerWidth <= 768) {
                            // 从pendingCards数组中移除已审核的申请
                            const cardIndex = pendingCards.findIndex(card => card.id === id);
                            if (cardIndex !== -1) {
                                // 移除已审核的卡片
                                pendingCards.splice(cardIndex, 1);

                                // 更新卡片计数
                                document.getElementById('totalCards').textContent = pendingCards.length;

                                // 如果没有更多待审核的申请，显示完成提示
                                if (pendingCards.length === 0) {
                                    const cardContainer = document.getElementById('pendingCardContainer');
                                    cardContainer.innerHTML = `
                                        <div class="text-center py-8">
                                            <p class="text-green-600 font-semibold text-lg">您已完成所有审核，请休息一下吧</p>
                                            <div class="mt-4">
                                                <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                </svg>
                                            </div>
                                        </div>
                                    `;

                                    // 更新当前卡片索引显示
                                    document.getElementById('currentCardIndex').textContent = "0";

                                    // 更新待审核数量
                                    updatePendingCount(0);
                                } else {
                                    // 如果删除的是当前显示的卡片，且不是最后一个卡片
                                    // 保持当前索引不变（这样会自动显示下一个卡片）
                                    if (cardIndex === currentCardIndex && currentCardIndex >= pendingCards.length) {
                                        currentCardIndex = pendingCards.length - 1;
                                    } else if (cardIndex < currentCardIndex) {
                                        // 如果删除的卡片在当前卡片之前，当前索引需要减1
                                        currentCardIndex--;
                                    }
                                    // 否则当前索引保持不变

                                    // 更新当前卡片索引显示
                                    document.getElementById('currentCardIndex').textContent = currentCardIndex + 1;

                                    // 更新待审核数量
                                    updatePendingCount(pendingCards.length);
                                }

                                // 重新渲染当前卡片
                                renderCurrentCard();

                                // 更新卡片导航按钮状态
                                updateCardNavButtons();
                            }
                        } else {
                            // 非移动端视图，更新待审核数量
                            updatePendingCount();
                        }
                    } else if (activeSection === '已审核') {
                        updateApprovedList();
                    }
                } else {
                    alert('审批失败：' + (result.message || '未知错误'));

                    // 重新启用审批下拉菜单
                    if (approvalSelect) {
                        approvalSelect.disabled = false;
                        approvalSelect.value = 'pending'; // 重置为待审批状态
                    }
                }
            } catch (error) {
                console.error('审批失败:', error);

                // 移除处理中提示（如果存在）
                const msgElement = document.getElementById('processingMessage');
                if (msgElement) {
                    document.body.removeChild(msgElement);
                }

                alert('审批失败: ' + (error.message || '网络错误'));

                // 重新启用审批下拉菜单
                const approvalSelect = document.querySelector(`select[onchange="approveApplication(${id}, this.value)"]`);
                if (approvalSelect) {
                    approvalSelect.disabled = false;
                    approvalSelect.value = 'pending'; // 重置为待审批状态
                }

                // 重置详情页面中的审核人选择框
                if (currentRole === 'chief' && error.message.includes('请至少选择一位经理')) {
                    // 取消所有经理选择框的选中状态
                    const managerCheckboxes = document.querySelectorAll(`.manager-checkbox`);
                    managerCheckboxes.forEach(checkbox => {
                        checkbox.checked = false;
                    });
                }
            }
        }

        async function viewDetail(id) {
            const app = applications.find(a => a.id === id);
            if (!app) return;

            // 检查当前用户是否有权查看此申请
            const isApplicant = app.username === currentUser;
            const isAdmin = currentRole === 'admin';
            const isReadOnly = currentRole === 'readonly';

            // 使用canUserApprove函数检查当前用户是否是审核人或已经审核过此申请
            const isApprover = canUserApprove(app);

            // 只读角色可以查看所有申请详情
            if (!isApplicant && !isAdmin && !isApprover && !isReadOnly) {
                alert('您无权查看此申请的详细信息');
                return;
            }

            // 确保已加载所有用户的签名数据
            if (!allUsers || allUsers.length === 0) {
                // 显示加载指示器
                const loadingIndicator = document.createElement('div');
                loadingIndicator.id = 'detailLoadingIndicator';
                loadingIndicator.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
                loadingIndicator.textContent = '正在加载签名数据，请稍候...';
                document.body.appendChild(loadingIndicator);

                try {
                    await loadAllUsers();
                } catch (error) {
                    console.error('加载用户签名数据出错:', error);
                } finally {
                    // 移除加载指示器
                    const indicator = document.getElementById('detailLoadingIndicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            }

            // 判断当前是否在待审核页面
            const isPendingApprovalPage = document.getElementById('pendingApprovalSection').classList.contains('hidden') === false;

            // 生成详情内容
            generateDetailContent(app, isPendingApprovalPage);

            // 显示详情模态框
            document.getElementById('detailModal').classList.remove('hidden');

            // 添加body类防止背景滚动
            document.body.classList.add('modal-open');

            // 应用模态框高度控制（如果已加载modal-height-control.js）
            if (typeof adjustModalHeight === 'function') {
                setTimeout(() => {
                    adjustModalHeight('detailModal');
                    if (typeof adjustDetailTabsHeight === 'function') {
                        adjustDetailTabsHeight();
                    }
                }, 10);
            }

            // 当总监查看待审批的申请时，加载经理列表
            if (isPendingApprovalPage && currentRole === 'chief' && app.status === '待总监审批') {
                const managersListContainer = document.getElementById(`managersList_${app.id}`);
                if (managersListContainer) {
                    renderManagersList(managersListContainer, []);

                    // 添加经理选择变化监听
                    setTimeout(() => {
                        const checkboxes = managersListContainer.querySelectorAll('.manager-checkbox');
                        checkboxes.forEach(checkbox => {
                            checkbox.addEventListener('change', function() {
                                updateSelectedManagersCount(app.id);
                            });
                        });

                        // 初始化选择计数
                        updateSelectedManagersCount(app.id);
                    }, 100);
                }
            }

            // 注释：附件上传事件监听在详情页面上传区域统一处理（第5061行），避免重复设置
        }

        // 生成申请详情内容
        function generateDetailContent(app, isPendingApprovalPage) {
            // 设置基本信息
            document.getElementById('detail-code').textContent = app.applicationCode || '无';
            document.getElementById('detail-applicant').textContent = sanitizeInput(app.applicant);
            document.getElementById('detail-department').textContent = sanitizeInput(app.department);
            document.getElementById('detail-date').textContent = app.date;
            document.getElementById('detail-status').textContent = `${app.status} (${getApplicationStatusDesc(app.status, app)})`;
            document.getElementById('detail-content').textContent = sanitizeInput(app.content);

            // 设置申请金额
            const amountEl = document.getElementById('detail-amount');
            if (app.amount) {
                const currency = app.currency || 'CNY';
                amountEl.textContent = `${formatCurrency(parseFloat(app.amount), currency)}`;

                // 不再显示美元约合人民币的等值

                amountEl.classList.remove('text-gray-500');
                amountEl.classList.add('text-blue-700');
            } else {
                amountEl.textContent = '无';
                amountEl.classList.remove('text-blue-700');
                amountEl.classList.add('text-gray-500');
            }

            // 设置操作按钮
            document.getElementById('detail-view-template').onclick = () => previewApplicationTemplate(app.id);

            // 撤回审批按钮
            const withdrawBtn = document.getElementById('detail-withdraw');
            if (canWithdrawApproval(app)) {
                withdrawBtn.classList.remove('hidden');
                withdrawBtn.onclick = () => handleWithdrawApproval(app.id);
            } else {
                withdrawBtn.classList.add('hidden');
            }

            // 设置厂长审批记录
            const directorsEl = document.getElementById('detail-directors-approvals');
            if (app.approvals.directors && Object.keys(app.approvals.directors).length > 0) {
                directorsEl.innerHTML = Object.entries(app.approvals.directors).map(([director, approval]) => {
                    // 检查是否有角色变更信息
                    let roleChangeInfo = '';
                    if (approval.originalRole && approval.roleChangedTo) {
                        roleChangeInfo = `<p class="text-xs text-blue-600 mt-1">注：此用户已从${approval.originalRole === 'director' ? '厂长' : approval.originalRole}变更为${getRoleDisplayName(approval.roleChangedTo)}</p>`;
                    }

                    return `
                        <div class="bg-white p-3 rounded border">
                            <div class="flex justify-between">
                                <span class="font-medium">厂长 ${sanitizeInput(director)}</span>
                                <span class="${approval.status === 'approved' ? 'text-green-600' : approval.status === 'rejected' ? 'text-red-600' : 'text-blue-600'}">
                                    ${statusMap[approval.status] || approval.status}
                                </span>
                            </div>
                            ${approval.comment ? `<p class="text-gray-600 mt-2">${sanitizeInput(approval.comment)}</p>` : ''}
                            ${approval.date ? `<p class="text-xs text-gray-500 mt-1">审批时间: ${formatDate(approval.date)}</p>` : ''}
                            ${roleChangeInfo}
                        </div>
                    `;
                }).join('');
            } else {
                directorsEl.innerHTML = '<p class="text-gray-500 text-center">暂无厂长审批信息</p>';
            }

            // 设置总监审批记录
            const chiefEl = document.getElementById('detail-chief-approval');
            if (app.approvals.chief && app.approvals.chief.status && app.approvals.chief.status !== 'pending') {
                // 检查是否有角色变更信息
                let roleChangeInfo = '';
                if (app.approvals.chief.originalRole && app.approvals.chief.roleChangedTo) {
                    const approverName = app.approvals.chief.approverUsername || '总监';
                    roleChangeInfo = `<p class="text-xs text-blue-600 mt-1">注：审批人 ${approverName} 已从${app.approvals.chief.originalRole === 'chief' ? '总监' : app.approvals.chief.originalRole}变更为${getRoleDisplayName(app.approvals.chief.roleChangedTo)}</p>`;
                }

                chiefEl.innerHTML = `
                    <div class="bg-white p-3 rounded border">
                        <div class="flex justify-between">
                            <span class="font-medium">总监审批</span>
                            <span class="${app.approvals.chief.status === 'approved' ? 'text-green-600' : 'text-red-600'}">
                                ${statusMap[app.approvals.chief.status] || app.approvals.chief.status}
                            </span>
                        </div>
                        ${app.approvals.chief.comment ? `<p class="text-gray-600 mt-2">${sanitizeInput(app.approvals.chief.comment)}</p>` : ''}
                        ${app.approvals.chief.date ? `<p class="text-xs text-gray-500 mt-1">审批时间: ${formatDate(app.approvals.chief.date)}</p>` : ''}
                        ${roleChangeInfo}
                    </div>
                `;
            } else {
                chiefEl.innerHTML = '<p class="text-gray-500 text-center">暂无总监审批信息</p>';
            }

            // 设置经理审批记录
            const managersEl = document.getElementById('detail-managers-approvals');
            if (app.approvals.managers && Object.keys(app.approvals.managers).length > 0) {
                const approvedManagers = Object.entries(app.approvals.managers).filter(([_, approval]) =>
                    approval.status && approval.status !== 'pending'
                );

                if (approvedManagers.length > 0) {
                    managersEl.innerHTML = approvedManagers.map(([manager, approval]) => {
                        // 检查是否有角色变更信息
                        let roleChangeInfo = '';
                        if (approval.originalRole && approval.roleChangedTo) {
                            roleChangeInfo = `<p class="text-xs text-blue-600 mt-1">注：此用户已从${approval.originalRole === 'manager' ? '经理' : approval.originalRole}变更为${getRoleDisplayName(approval.roleChangedTo)}</p>`;
                        }

                        return `
                            <div class="bg-white p-3 rounded border">
                                <div class="flex justify-between">
                                    <span class="font-medium">经理 ${sanitizeInput(manager)}</span>
                                    <span class="${approval.status === 'approved' ? 'text-green-600' : 'text-red-600'}">
                                        ${statusMap[approval.status] || approval.status}
                                    </span>
                                </div>
                                ${approval.comment ? `<p class="text-gray-600 mt-2">${sanitizeInput(approval.comment)}</p>` : ''}
                                ${approval.date ? `<p class="text-xs text-gray-500 mt-1">审批时间: ${formatDate(approval.date)}</p>` : ''}
                                ${roleChangeInfo}
                            </div>
                        `;
                    }).join('');
                } else {
                    managersEl.innerHTML = '<p class="text-gray-500 text-center">经理审批进行中，暂无审批结果</p>';
                }
            } else {
                managersEl.innerHTML = '<p class="text-gray-500 text-center">暂无经理审批信息</p>';
            }

            // 设置CEO审批信息
            const ceoEl = document.getElementById('detail-ceo');
            if (app.approvals.ceo && (app.approvals.ceo.status === 'approved' || app.approvals.ceo.status === 'rejected')) {
                const ceoApproval = app.approvals.ceo;

                // 检查是否有角色变更信息
                let roleChangeInfo = '';
                if (ceoApproval.originalRole && ceoApproval.roleChangedTo) {
                    const approverName = ceoApproval.approverUsername || 'CEO';
                    roleChangeInfo = `<p class="text-xs text-blue-600 mt-1">注：审批人 ${approverName} 已从${ceoApproval.originalRole === 'ceo' ? 'CEO' : getRoleDisplayName(ceoApproval.originalRole)}变更为${getRoleDisplayName(ceoApproval.roleChangedTo)}</p>`;
                }

                ceoEl.innerHTML = `
                    <div class="bg-white p-3 rounded border">
                        <div class="flex justify-between">
                            <span class="font-medium">CEO</span>
                            <span class="${ceoApproval.status === 'approved' ? 'text-green-600' : 'text-red-600'}">
                                ${statusMap[ceoApproval.status] || ceoApproval.status}
                            </span>
                        </div>
                        ${ceoApproval.comment ? `<p class="text-gray-600 mt-2">${sanitizeInput(ceoApproval.comment)}</p>` : ''}
                        ${ceoApproval.date ? `<p class="text-xs text-gray-500 mt-1">审批时间: ${formatDate(ceoApproval.date)}</p>` : ''}
                        ${roleChangeInfo}
                    </div>
                `;
            } else {
                ceoEl.innerHTML = '<p class="text-gray-500 text-center">暂无CEO审批信息</p>';
            }

            // 设置初始附件
            const attachmentsEl = document.getElementById('detail-attachments');
            if (app.attachments && app.attachments.length > 0) {
                attachmentsEl.innerHTML = app.attachments.map(f => `
                    <div class="flex items-center justify-between bg-white p-3 rounded border">
                        <span class="truncate max-w-xs">${sanitizeInput(decodeURIComponent(f.name))}</span>
                        <div class="flex space-x-2">
                            <button onclick="previewFile('${f.path}', '${encodeURIComponent(f.name)}')" class="text-green-600 hover:text-green-800 text-sm px-3 py-1 rounded border border-green-200 hover:bg-green-50">预览</button>
                            <a href="/download/${f.path}?name=${encodeURIComponent(f.name)}" class="text-blue-600 hover:text-blue-800 text-sm px-3 py-1 rounded border border-blue-200 hover:bg-blue-50" download="${decodeURIComponent(f.name)}">下载</a>
                        </div>
                    </div>
                `).join('');
            } else {
                attachmentsEl.innerHTML = '<p class="text-gray-500 text-center">无附件</p>';
            }

            // 设置审批附件
            const approvalAttachmentsEl = document.getElementById('detail-approval-attachments');
            let allApprovalAttachments = [];

            // 收集所有审批附件
            if (app.approvals.directors) {
                Object.entries(app.approvals.directors).forEach(([director, approval]) => {
                    if (approval.attachments && approval.attachments.length > 0) {
                        approval.attachments.forEach(f => {
                            allApprovalAttachments.push({
                                ...f,
                                approver: `厂长 ${director}`
                            });
                        });
                    }
                });
            }

            if (app.approvals.chief && app.approvals.chief.attachments && app.approvals.chief.attachments.length > 0) {
                app.approvals.chief.attachments.forEach(f => {
                    allApprovalAttachments.push({
                        ...f,
                        approver: '总监'
                    });
                });
            }

            if (app.approvals.managers) {
                Object.entries(app.approvals.managers).forEach(([manager, approval]) => {
                    if (approval.attachments && approval.attachments.length > 0) {
                        approval.attachments.forEach(f => {
                            allApprovalAttachments.push({
                                ...f,
                                approver: `经理 ${manager}`
                            });
                        });
                    }
                });
            }

            if (app.approvals.ceo && app.approvals.ceo.attachments && app.approvals.ceo.attachments.length > 0) {
                app.approvals.ceo.attachments.forEach(f => {
                    allApprovalAttachments.push({
                        ...f,
                        approver: 'CEO'
                    });
                });
            }

            if (allApprovalAttachments.length > 0) {
                approvalAttachmentsEl.innerHTML = allApprovalAttachments.map(f => `
                    <div class="flex items-center justify-between bg-white p-3 rounded border">
                        <div>
                            <span class="truncate max-w-xs block">${sanitizeInput(decodeURIComponent(f.name))}</span>
                            <span class="text-xs text-gray-500">上传者: ${f.approver}</span>
                        </div>
                        <div class="flex space-x-2">
                            <button onclick="previewFile('${f.path}', '${encodeURIComponent(f.name)}')" class="text-green-600 hover:text-green-800 text-sm px-3 py-1 rounded border border-green-200 hover:bg-green-50">预览</button>
                            <a href="/download/${f.path}?name=${encodeURIComponent(f.name)}" class="text-blue-600 hover:text-blue-800 text-sm px-3 py-1 rounded border border-blue-200 hover:bg-blue-50" download="${decodeURIComponent(f.name)}">下载</a>
                        </div>
                    </div>
                `).join('');
            } else {
                approvalAttachmentsEl.innerHTML = '<p class="text-gray-500 text-center">无审批附件</p>';
            }

            // 上传新附件区域
            const uploadSectionEl = document.getElementById('detail-upload-section');
            if (isPendingApprovalPage && canApprove(app)) {
                uploadSectionEl.classList.remove('hidden');

                // 设置附件上传事件
                const fileInput = document.getElementById('detail-new-attachments');
                fileInput.onchange = function() {
                    const files = Array.from(this.files);
                    const validFiles = files.filter(file =>
                        file.size <= 5 * 1024 * 1024 && /\.(pdf|doc|docx|jpg|jpeg|png)$/i.test(file.name)
                    );
                    const invalidFiles = files.filter(file => !validFiles.includes(file));

                    if (invalidFiles.length > 0) {
                        alert(`以下文件不符合要求：\n${invalidFiles.map(f => f.name).join('\n')}`);
                    }

                    // 如果有有效文件，弹出文件命名规范提醒
                    if (validFiles.length > 0) {
                        alert('请确认您上传的附件文档名称符合基本文件描述规范');
                    }

                    // 显示已选择的文件列表
                    const attachmentsList = document.getElementById('detail-new-attachments-list');
                    attachmentsList.innerHTML = validFiles.map(file => `
                        <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                            <div class="flex items-center space-x-2">
                                <span class="text-sm">${sanitizeInput(file.name)}</span>
                                <span class="text-xs text-gray-400">${(file.size/1024/1024).toFixed(2)}MB</span>
                            </div>
                        </div>
                    `).join('');
                };

                // 设置确认附件按钮
                document.getElementById('detail-confirm-attachments').onclick = () => confirmDetailAttachments(app.id);
            } else {
                uploadSectionEl.classList.add('hidden');
            }

            // 审批操作区域
            const approvalActionsEl = document.getElementById('detail-approval-actions');
            if (isPendingApprovalPage && canApprove(app)) {
                approvalActionsEl.classList.remove('hidden');

                // 经理选择区域（仅总监可见）
                const managersSelectionEl = document.getElementById('detail-managers-selection');
                if (currentRole === 'chief' && app.status === '待总监审批') {
                    managersSelectionEl.classList.remove('hidden');

                    // 重置经理选择区域状态为默认折叠
                    if (typeof resetManagersSelectionState === 'function') {
                        resetManagersSelectionState();
                    }

                    // 渲染经理列表
                    renderManagersList(document.getElementById('detail-managers-list'), []);

                    // 添加经理选择变化监听
                    setTimeout(() => {
                        const checkboxes = document.querySelectorAll('.manager-checkbox');
                        checkboxes.forEach(checkbox => {
                            checkbox.addEventListener('change', function() {
                                updateSelectedManagersCount(app.id);
                            });
                        });

                        // 初始化选择计数
                        updateSelectedManagersCount(app.id);
                    }, 100);

                    // 设置确认选择按钮
                    document.getElementById('detail-confirm-managers').onclick = () => confirmSelectedManagers(app.id);
                } else {
                    managersSelectionEl.classList.add('hidden');
                }

                // 设置审批按钮
                document.getElementById('detail-approve').onclick = () => approveApplication(app.id, 'approved');
                document.getElementById('detail-reject').onclick = () => approveApplication(app.id, 'rejected');
            } else {
                approvalActionsEl.classList.add('hidden');
            }

            // 设置标签页切换
            setupDetailTabs();
        }

        // 标签页切换逻辑
        function setupDetailTabs() {
            const tabs = ['basic', 'approvals', 'attachments'];

            tabs.forEach(tab => {
                document.getElementById(`tab-${tab}`).addEventListener('click', () => {
                    // 更新标签页样式
                    tabs.forEach(t => {
                        const tabEl = document.getElementById(`tab-${t}`);
                        const contentEl = document.getElementById(`content-${t}`);

                        if (t === tab) {
                            tabEl.classList.add('border-b-2', 'border-blue-500', 'text-blue-600');
                            tabEl.classList.remove('text-gray-500');
                            contentEl.classList.remove('hidden');
                        } else {
                            tabEl.classList.remove('border-b-2', 'border-blue-500', 'text-blue-600');
                            tabEl.classList.add('text-gray-500');
                            contentEl.classList.add('hidden');
                        }
                    });
                });
            });
        }

        function closeDetail() {
            document.getElementById('detailModal').classList.add('hidden');

            // 移除body类，恢复背景滚动
            document.body.classList.remove('modal-open');

            // 重置滚动位置
            const modalContent = document.querySelector('#detailModal .relative');
            if (modalContent) {
                modalContent.scrollTop = 0;
            }

            // 重置经理选择区域状态
            if (typeof resetManagersSelectionState === 'function') {
                resetManagersSelectionState();
            }

            // 重置标签页状态，确保下次打开时显示基本信息标签页
            const tabs = ['basic', 'approvals', 'attachments'];
            tabs.forEach(t => {
                const tabEl = document.getElementById(`tab-${t}`);
                const contentEl = document.getElementById(`content-${t}`);

                if (t === 'basic') {
                    tabEl.classList.add('border-b-2', 'border-blue-500', 'text-blue-600');
                    tabEl.classList.remove('text-gray-500');
                    contentEl.classList.remove('hidden');
                } else {
                    tabEl.classList.remove('border-b-2', 'border-blue-500', 'text-blue-600');
                    tabEl.classList.add('text-gray-500');
                    contentEl.classList.add('hidden');
                }
            });

            // 清空文件上传区域
            const fileInput = document.getElementById('detail-new-attachments');
            if (fileInput) {
                fileInput.value = '';
            }

            const attachmentsList = document.getElementById('detail-new-attachments-list');
            if (attachmentsList) {
                attachmentsList.innerHTML = '';
            }
        }

        // 格式化金额为千分位格式，支持不同币种
        function formatCurrency(amount, currency = 'CNY') {
            const formattedAmount = amount.toLocaleString('zh-CN', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
            if (currency === 'USD') {
                return '$' + formattedAmount;
            } else {
                return '¥' + formattedAmount;
            }
        }

        // 格式化日期显示
        function formatDate(dateString) {
            if (!dateString) return '';

            try {
                const date = new Date(dateString);
                if (isNaN(date.getTime())) return dateString; // 如果日期无效，返回原始字符串

                return date.toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit',
                    hour12: false
                });
            } catch (error) {
                console.error('日期格式化错误:', error);
                return dateString;
            }
        }

        // 获取角色显示名称
        function getRoleDisplayName(role) {
            const roleNames = {
                'user': '普通用户',
                'director': '厂长',
                'chief': '总监',
                'manager': '经理',
                'ceo': 'CEO',
                'admin': '管理员',
                'readonly': '只读用户'
            };
            return roleNames[role] || role;
        }

        // 计算当前页金额合计，分别计算人民币和美元
        function calculateTotalAmount(apps) {
            let totalCNY = 0;
            let totalUSD = 0;

            // 遍历应用列表，按币种累加金额
            apps.forEach(app => {
                if (app.amount) {
                    const amount = parseFloat(app.amount);
                    if (!isNaN(amount)) {
                        if (app.currency === 'USD') {
                            totalUSD += amount;
                        } else {
                            totalCNY += amount;
                        }
                    }
                }
            });

            // 更新合计金额显示，使用千分位格式
            const totalAmountElement = document.getElementById('currentPageTotalAmount');

            // 构建显示文本
            let displayText = '';

            if (totalCNY > 0) {
                displayText += formatCurrency(totalCNY, 'CNY');
            }

            if (totalUSD > 0) {
                if (displayText) {
                    displayText += '<br>';
                }
                displayText += formatCurrency(totalUSD, 'USD');
            }

            if (!displayText) {
                displayText = '¥0.00';
            }

            totalAmountElement.innerHTML = displayText;
        }

        // 文件预览函数 - 支持移动端和PC端分离
        function previewFile(filePath, fileName) {
            const previewFrame = document.getElementById('previewFrame');
            const mobilePdfPreview = document.getElementById('mobilePdfPreview');
            const imagePreview = document.getElementById('imagePreview');
            const previewImage = document.getElementById('previewImage');
            const previewFileName = document.getElementById('previewFileName');
            const unsupportedPreview = document.getElementById('unsupportedPreview');
            const unsupportedDownloadLink = document.getElementById('unsupportedDownloadLink');

            // 设置文件名
            previewFileName.textContent = decodeURIComponent(fileName);

            // 获取文件扩展名
            const fileExt = fileName.split('.').pop().toLowerCase();

            // 检测是否为移动设备
            const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            // 隐藏所有预览容器
            previewFrame.classList.add('hidden');
            mobilePdfPreview.classList.add('hidden');
            imagePreview.classList.add('hidden');
            unsupportedPreview.classList.add('hidden');

            // 根据文件类型选择不同的预览方式
            if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'webp'].includes(fileExt)) {
                // 图片预览
                imagePreview.classList.remove('hidden');
                previewImage.src = `/preview/${filePath}`;

                // 确保图片加载完成后调整大小
                previewImage.onload = function() {
                    if (previewImage.naturalHeight > window.innerHeight * 0.8) {
                        previewImage.style.maxHeight = '80vh';
                    }
                };
            } else if (fileExt === 'pdf') {
                // PDF预览 - 移动端和PC端分离处理
                if (isMobile) {
                    // 移动端使用PDF.js渲染
                    mobilePdfPreview.classList.remove('hidden');
                    loadMobilePdfPreview(filePath);
                } else {
                    // PC端使用iframe
                    previewFrame.classList.remove('hidden');

                    // 清除之前的src，避免缓存问题
                    previewFrame.src = '';

                    // 设置新的src
                    setTimeout(() => {
                        previewFrame.src = `/preview/${filePath}`;
                    }, 50);

                    // 监听iframe加载事件
                    previewFrame.onload = function() {
                        try {
                            // 尝试调整iframe高度以适应内容
                            const frameHeight = previewFrame.contentWindow.document.body.scrollHeight;
                            if (frameHeight > 0) {
                                previewFrame.style.height = Math.min(frameHeight, window.innerHeight * 0.8) + 'px';
                            }
                        } catch (e) {
                            console.log('无法访问iframe内容，可能是跨域限制');
                        }
                    };
                }
            } else if (['doc', 'docx', 'xls', 'xlsx', 'ppt', 'pptx', 'txt'].includes(fileExt)) {
                // 其他文档预览 - 统一使用iframe
                previewFrame.classList.remove('hidden');

                // 清除之前的src，避免缓存问题
                previewFrame.src = '';

                // 设置新的src
                setTimeout(() => {
                    previewFrame.src = `/preview/${filePath}`;
                }, 50);

                // 监听iframe加载事件
                previewFrame.onload = function() {
                    try {
                        // 尝试调整iframe高度以适应内容
                        const frameHeight = previewFrame.contentWindow.document.body.scrollHeight;
                        if (frameHeight > 0) {
                            previewFrame.style.height = Math.min(frameHeight, window.innerHeight * 0.8) + 'px';
                        }
                    } catch (e) {
                        console.log('无法访问iframe内容，可能是跨域限制');
                    }
                };
            } else {
                // 不支持预览的文件类型
                unsupportedPreview.classList.remove('hidden');
                unsupportedDownloadLink.href = `/download/${filePath}?name=${encodeURIComponent(fileName)}`;
                unsupportedDownloadLink.setAttribute('download', decodeURIComponent(fileName));
            }

            // 显示预览模态框
            document.getElementById('previewModal').classList.remove('hidden');
        }

        // 关闭预览模态框
        function closePreview() {
            document.getElementById('previewModal').classList.add('hidden');
            document.getElementById('previewFrame').src = '';
            document.getElementById('previewImage').src = '';
            document.getElementById('unsupportedDownloadLink').href = '#';

            // 清理移动端PDF预览
            const mobilePdfPreview = document.getElementById('mobilePdfPreview');
            if (mobilePdfPreview && !mobilePdfPreview.classList.contains('hidden')) {
                cleanupMobilePdfPreview();
            }

            // 重置滚动位置
            document.getElementById('previewModal').scrollTop = 0;
        }

        // 移动端PDF预览相关变量
        let currentPdfDoc = null;
        let currentPageNum = 1;
        let totalPages = 0;
        let pdfScale = 1.5; // 适中的缩放比例
        let isRendering = false; // 防止重复渲染

        // 加载移动端PDF预览
        async function loadMobilePdfPreview(filePath) {
            console.log('=== 开始加载移动端PDF预览 ===');
            console.log('文件路径:', filePath);

            const loadingIndicator = document.getElementById('pdfLoadingIndicator');
            const pageInfo = document.getElementById('pageInfo');

            try {
                // 清理之前的状态
                cleanupMobilePdfPreview();

                // 显示加载指示器
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'flex';
                }

                // 确保PDF.js已加载
                await window.loadPdfJsLibrary();
                console.log('✓ PDF.js库已加载');

                // 加载PDF文档
                console.log('正在加载PDF文档...');
                const loadingTask = pdfjsLib.getDocument({
                    url: `/preview/${filePath}`,
                    cMapUrl: 'js/libs/cmaps/',
                    cMapPacked: true
                });

                currentPdfDoc = await loadingTask.promise;
                totalPages = currentPdfDoc.numPages;
                currentPageNum = 1;

                console.log(`✓ PDF加载成功，总页数: ${totalPages}`);

                // 更新页面信息
                if (pageInfo) {
                    pageInfo.textContent = `第 ${currentPageNum} 页，共 ${totalPages} 页`;
                }

                // 初始化触摸手势
                initializePdfTouchGestures();

                // 渲染第一页
                await renderPdfPage(currentPageNum);

                // 隐藏加载指示器
                if (loadingIndicator) {
                    loadingIndicator.style.display = 'none';
                }

                console.log('✓ PDF预览加载完成');

                // 显示滑动提示（仅在多页面时）
                if (totalPages > 1) {
                    showPdfSwipeHint();
                }

            } catch (error) {
                console.error('❌ PDF加载失败:', error);
                if (loadingIndicator) {
                    loadingIndicator.innerHTML = `
                        <div class="text-center">
                            <div class="text-red-600 mb-2">PDF加载失败</div>
                            <div class="text-sm text-gray-500">${error.message}</div>
                            <button onclick="closePreview()" class="mt-2 px-4 py-2 bg-blue-500 text-white rounded">关闭</button>
                        </div>
                    `;
                }
            }
        }

        // 初始化PDF触摸手势
        function initializePdfTouchGestures() {
            const pdfContainer = document.getElementById('pdfContainer');

            if (!pdfContainer) {
                console.error('❌ 找不到PDF容器');
                return;
            }

            let touchStartX = 0;
            let touchStartY = 0;
            let touchEndX = 0;
            let touchEndY = 0;

            pdfContainer.addEventListener('touchstart', function(e) {
                touchStartX = e.changedTouches[0].screenX;
                touchStartY = e.changedTouches[0].screenY;
            }, { passive: true });

            pdfContainer.addEventListener('touchend', function(e) {
                touchEndX = e.changedTouches[0].screenX;
                touchEndY = e.changedTouches[0].screenY;
                handlePdfSwipe();
            }, { passive: true });

            function handlePdfSwipe() {
                if (isRendering) {
                    console.log('⏳ 正在渲染中，跳过滑动操作');
                    return;
                }

                const deltaX = touchEndX - touchStartX;
                const deltaY = touchEndY - touchStartY;
                const minSwipeDistance = 50;

                // 只有水平滑动距离大于垂直滑动距离时才处理翻页
                if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > minSwipeDistance) {
                    if (deltaX > 0) {
                        // 向右滑动 - 上一页
                        if (currentPageNum > 1) {
                            console.log('👉 向右滑动 - 上一页');
                            currentPageNum--;
                            renderPdfPage(currentPageNum);
                            updatePageInfo();
                        }
                    } else {
                        // 向左滑动 - 下一页
                        if (currentPageNum < totalPages) {
                            console.log('👈 向左滑动 - 下一页');
                            currentPageNum++;
                            renderPdfPage(currentPageNum);
                            updatePageInfo();
                        }
                    }
                }
            }

            console.log('✓ 触摸手势支持已添加');
        }

        // 更新页面信息
        function updatePageInfo() {
            const pageInfo = document.getElementById('pageInfo');
            if (pageInfo) {
                pageInfo.textContent = `第 ${currentPageNum} 页，共 ${totalPages} 页`;
            }
        }

        // 渲染PDF页面 - 高分辨率优化版本
        async function renderPdfPage(pageNum) {
            if (isRendering) {
                console.log('⏳ 正在渲染中，跳过重复渲染');
                return;
            }

            if (!currentPdfDoc) {
                console.error('❌ PDF文档未加载');
                return;
            }

            isRendering = true;
            console.log(`=== 开始渲染第 ${pageNum} 页 ===`);

            try {
                // 获取页面
                const page = await currentPdfDoc.getPage(pageNum);
                console.log('✓ 获取PDF页面成功');

                const canvas = document.getElementById('pdfCanvas');
                const context = canvas.getContext('2d');

                if (!canvas || !context) {
                    throw new Error('无法获取Canvas元素或上下文');
                }

                // 等待DOM更新
                await new Promise(resolve => requestAnimationFrame(resolve));

                // 获取容器尺寸
                const container = document.getElementById('pdfContainer');
                const containerWidth = container.clientWidth - 20; // 减去padding

                console.log(`容器宽度: ${containerWidth}`);

                // 获取设备像素比，确保高分辨率显示
                const devicePixelRatio = window.devicePixelRatio || 1;
                console.log(`设备像素比: ${devicePixelRatio}`);

                // 获取页面原始尺寸
                const originalViewport = page.getViewport({ scale: 1.0 });
                console.log(`原始页面尺寸: ${originalViewport.width} x ${originalViewport.height}`);

                // 计算显示缩放比例（适应容器宽度）
                const displayScale = Math.min(containerWidth / originalViewport.width, 2.5);

                // 计算渲染缩放比例（考虑设备像素比以获得清晰显示）
                const renderScale = displayScale * devicePixelRatio;

                console.log(`显示缩放: ${displayScale}, 渲染缩放: ${renderScale}`);

                // 获取渲染视口（用于实际渲染）
                const renderViewport = page.getViewport({ scale: renderScale });

                // 获取显示视口（用于CSS显示尺寸）
                const displayViewport = page.getViewport({ scale: displayScale });

                // 设置canvas的实际渲染尺寸（高分辨率）
                canvas.width = renderViewport.width;
                canvas.height = renderViewport.height;

                // 设置canvas的CSS显示尺寸
                canvas.style.width = displayViewport.width + 'px';
                canvas.style.height = displayViewport.height + 'px';

                console.log(`Canvas实际尺寸: ${canvas.width} x ${canvas.height}`);
                console.log(`Canvas显示尺寸: ${displayViewport.width} x ${displayViewport.height}`);

                // 缩放绘图上下文以匹配设备像素比
                context.scale(devicePixelRatio, devicePixelRatio);

                // 清除画布
                context.clearRect(0, 0, canvas.width / devicePixelRatio, canvas.height / devicePixelRatio);

                // 设置白色背景
                context.fillStyle = '#ffffff';
                context.fillRect(0, 0, canvas.width / devicePixelRatio, canvas.height / devicePixelRatio);

                // 重置缩放以进行PDF渲染
                context.setTransform(1, 0, 0, 1, 0, 0);

                // 渲染页面
                const renderContext = {
                    canvasContext: context,
                    viewport: renderViewport
                };

                console.log('🎨 开始渲染PDF页面...');
                await page.render(renderContext).promise;
                console.log('✓ PDF页面渲染完成');

                // 更新页面信息
                updatePageInfo();

            } catch (error) {
                console.error('❌ PDF页面渲染失败:', error);

                // 显示错误信息
                const canvas = document.getElementById('pdfCanvas');
                if (canvas) {
                    const context = canvas.getContext('2d');
                    if (context) {
                        // 设置canvas基本尺寸
                        canvas.width = 400;
                        canvas.height = 300;
                        canvas.style.width = '400px';
                        canvas.style.height = '300px';

                        // 清除并绘制错误信息
                        context.clearRect(0, 0, canvas.width, canvas.height);
                        context.fillStyle = '#f3f4f6';
                        context.fillRect(0, 0, canvas.width, canvas.height);

                        context.fillStyle = '#ef4444';
                        context.font = '16px Arial';
                        context.textAlign = 'center';
                        context.fillText('页面渲染失败', canvas.width / 2, canvas.height / 2 - 10);

                        context.fillStyle = '#6b7280';
                        context.font = '12px Arial';
                        context.fillText(error.message, canvas.width / 2, canvas.height / 2 + 20);
                    }
                }
            } finally {
                isRendering = false;
                console.log('=== 渲染流程结束 ===');
            }
        }



        // 清理移动端PDF预览
        function cleanupMobilePdfPreview() {
            console.log('=== 清理PDF预览状态 ===');

            // 重置渲染状态
            isRendering = false;

            // 销毁PDF文档
            if (currentPdfDoc) {
                try {
                    currentPdfDoc.destroy();
                    console.log('✓ PDF文档已销毁');
                } catch (e) {
                    console.warn('⚠️ PDF文档销毁时出错:', e);
                }
                currentPdfDoc = null;
            }

            // 重置变量
            currentPageNum = 1;
            totalPages = 0;

            // 清空canvas
            const canvas = document.getElementById('pdfCanvas');
            if (canvas) {
                try {
                    const context = canvas.getContext('2d');
                    context.clearRect(0, 0, canvas.width, canvas.height);
                    // 重置canvas尺寸
                    canvas.width = 100;
                    canvas.height = 100;
                    canvas.style.width = '100px';
                    canvas.style.height = '100px';
                    console.log('✓ Canvas已清理');
                } catch (e) {
                    console.warn('⚠️ 清理canvas时出错:', e);
                }
            }

            // 重置UI
            const pageInfo = document.getElementById('pageInfo');
            const loadingIndicator = document.getElementById('pdfLoadingIndicator');

            if (pageInfo) {
                pageInfo.textContent = '第 1 页，共 1 页';
            }

            if (loadingIndicator) {
                loadingIndicator.style.display = 'none';
                loadingIndicator.innerHTML = `
                    <div class="text-center">
                        <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-2"></div>
                        <div class="text-gray-600">正在加载PDF...</div>
                    </div>
                `;
            }

            console.log('✓ PDF预览状态已清理');
        }

        // 显示PDF滑动提示
        function showPdfSwipeHint() {
            const swipeHint = document.getElementById('swipeHint');
            if (swipeHint) {
                swipeHint.style.opacity = '1';
                setTimeout(() => {
                    swipeHint.style.opacity = '0';
                }, 3000); // 3秒后隐藏提示
            }
        }

        function logout() {
            // 清除会话存储
            sessionStorage.removeItem('isLoggedIn');
            sessionStorage.removeItem('username');
            sessionStorage.removeItem('role');
            sessionStorage.removeItem('department');
            sessionStorage.removeItem('loginTimestamp');
            sessionStorage.removeItem('lastActivity');
            sessionStorage.removeItem('currentSection'); // 清除当前页面状态

            // 清除可能存在的旧版本localStorage数据（确保完全清除）
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            localStorage.removeItem('role');
            localStorage.removeItem('department');
            localStorage.removeItem('currentSection');

            // 清除当前用户状态
            currentUser = null;
            currentRole = null;
            currentDepartment = null;

            // 清除浏览器缓存（通过重新加载页面并跳过缓存）
            window.location.reload(true);

            // 以下代码在页面重新加载后不会执行，但保留以防重定向失败
            showLoginForm();
            updateUserDisplay();
        }

        // 加载审批人列表
        async function loadApprovers() {
            try {
                const response = await fetch('/approvers');
                const result = await response.json();
                if (result.success) {
                    // 保存审批人列表
                    window.directorsData = result.directors;
                    window.managersData = result.managers;

                    // 渲染厂长列表到新建申请表单
                    renderDirectorsList(document.getElementById('directorsList'), []);

                    // 渲染厂长列表到编辑表单
                    renderDirectorsList(document.getElementById('editDirectorsList'), []);
                }
            } catch (error) {
                console.error('加载审批人失败:', error);
            }
        }

        // 渲染厂长列表
        function renderDirectorsList(container, selectedDirectors = []) {
            if (!window.directorsData || window.directorsData.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center">暂无厂长用户</p>';
                return;
            }

            container.innerHTML = window.directorsData.map(director => `
                <div class="flex items-center p-1">
                    <input type="checkbox" id="director-${director.username}"
                        value="${director.username}"
                        ${selectedDirectors.includes(director.username) ? 'checked' : ''}
                        class="mr-2 director-checkbox">
                    <label for="director-${director.username}" class="text-sm">
                        ${sanitizeInput(director.username)} (${director.department || '未设置部门'})
                    </label>
                </div>
            `).join('');
        }

        // 渲染经理列表
        function renderManagersList(container, selectedManagers = []) {
            if (!window.managersData || window.managersData.length === 0) {
                container.innerHTML = '<p class="text-gray-500 text-center">暂无经理用户</p>';
                return;
            }

            // 从container.id中提取appId
            const appId = container.id.split('_')[1];

            container.innerHTML = window.managersData.map(manager => `
                <div class="flex items-center p-1">
                    <input type="checkbox" id="manager_${appId}_${manager.username}"
                        value="${manager.username}"
                        ${selectedManagers.includes(manager.username) ? 'checked' : ''}
                        class="mr-2 manager-checkbox">
                    <label for="manager_${appId}_${manager.username}" class="text-sm">
                        ${sanitizeInput(manager.username)}
                    </label>
                </div>
            `).join('');

            // 重置确认状态
            if (appId) {
                const confirmStatus = document.getElementById(`managersConfirmStatus_${appId}`);
                if (confirmStatus) {
                    confirmStatus.classList.add('hidden');
                }
            }
        }

        // 获取选中的厂长
        function getSelectedDirectors(container) {
            const checkboxes = container.querySelectorAll('.director-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 获取选中的经理
        function getSelectedManagers(container) {
            const checkboxes = container.querySelectorAll('.manager-checkbox:checked');
            return Array.from(checkboxes).map(cb => cb.value);
        }

        // 确认申请详情中的附件
        function confirmDetailAttachments(appId) {
            const fileInput = document.getElementById('detail-new-attachments');
            const files = Array.from(fileInput.files);
            if (files.length === 0) {
                alert('请先选择要上传的附件');
                return;
            }

            // 显示已选择的文件列表
            const attachmentsList = document.getElementById('detail-new-attachments-list');
            attachmentsList.innerHTML = files.map(file => `
                <div class="flex items-center justify-between p-2 bg-gray-50 rounded-md">
                    <div class="flex items-center space-x-2">
                        <span class="text-sm attachment-filename">${sanitizeInput(file.name)}</span>
                        <span class="text-xs text-gray-400">${(file.size/1024/1024).toFixed(2)}MB</span>
                    </div>
                    <span class="text-green-600 text-sm">✓ 已确认</span>
                </div>
            `).join('');

            // 简单确认附件，不进行上传
            // 上传将在审批时一起进行
            alert('附件确认成功，共选择了 ' + files.length + ' 个文件');
        }

        // 更新待审核列表
        async function updatePendingApprovalList(page = 1) {
            console.log('更新待审核列表，当前角色:', currentRole);

            // 筛选出当前用户需要审核的申请
            let pendingApps = applications.filter(app => {
                // 检查申请状态是否需要当前用户角色的审核
                const needsApproval = needsApprovalByCurrentUser(app.status);

                if (!needsApproval) {
                    return false;
                }

                // 根据不同角色进行筛选
                if (currentRole === 'admin' || currentRole === 'readonly') {
                    // 管理员和只读用户可以看到所有待审核的申请
                    return true;
                } else if (currentRole === 'director') {
                    // 厂长只能看到指定自己审核且状态为待审核的申请
                    return app.approvals &&
                           app.approvals.directors &&
                           app.approvals.directors[currentUser] &&
                           app.approvals.directors[currentUser].status === 'pending';
                } else if (currentRole === 'chief') {
                    // 总监只能看到待总监审批的申请
                    const isWaitingForChief = app.status === '待总监审批';
                    console.log(`检查申请ID=${app.id || 'unknown'}, 状态="${app.status}", 是否显示给总监: ${isWaitingForChief}`);
                    return isWaitingForChief;
                } else if (currentRole === 'manager') {
                    // 经理只能看到指定自己审核且状态为待审核的申请
                    return app.approvals &&
                           app.approvals.managers &&
                           app.approvals.managers[currentUser] &&
                           app.approvals.managers[currentUser].status === 'pending';
                } else if (currentRole === 'ceo') {
                    // CEO只能看到待CEO审批的申请
                    const isWaitingForCEO = app.status === '待CEO审批';
                    console.log(`检查申请ID=${app.id || 'unknown'}, 状态="${app.status}", 是否显示给CEO: ${isWaitingForCEO}`);
                    return isWaitingForCEO;
                }

                return false;
            });

            console.log('待审核应用数量:', pendingApps.length);

            // 按优先级排序（紧急 > 中等 > 普通）
            const sortedApps = pendingApps.sort((a, b) => {
                const priorityOrder = { 'high': 0, 'medium': 1, 'normal': 2 };
                return priorityOrder[a.priority] - priorityOrder[b.priority];
            });

            // 分页处理
            const pageSize = 10; // 每页显示10条记录
            const totalItems = sortedApps.length;
            const totalPages = Math.ceil(totalItems / pageSize);

            // 确保当前页在有效范围内
            page = Math.max(1, Math.min(page, totalPages || 1));

            // 获取当前页的数据
            const startIndex = (page - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, totalItems);
            const currentPageData = sortedApps.slice(startIndex, endIndex);

            // 渲染当前页数据
            const tbody = document.getElementById('pendingApprovalList');
            if (currentPageData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-8 text-center"><p class="text-green-600 font-semibold text-lg">您已完成所有审核，请休息一下吧</p><div class="mt-4"><svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" /></svg></div></td></tr>';

                // 更新分页控件
                updatePendingPagination(page, totalPages, totalItems);

                // 更新移动端卡片视图，显示完成提示
                updatePendingCards([]);

                return;
            }

            // 使用原有的renderApplicationsList函数渲染待审核列表
            renderApplicationsList(currentPageData, tbody);

            // 更新分页控件
            updatePendingPagination(page, totalPages, totalItems);

            // 更新待审核数量
            updatePendingCount(pendingApps.length);

            // 更新移动端卡片视图
            updatePendingCards(sortedApps);
        }

        // 更新待审核分页控件
        function updatePendingPagination(currentPage, totalPages, totalItems) {
            const prevPageBtn = document.getElementById('pendingPrevPage');
            const nextPageBtn = document.getElementById('pendingNextPage');
            const pageNumbersContainer = document.getElementById('pendingPageNumbers');
            const totalItemsSpan = document.getElementById('pendingTotalItems');

            // 更新总记录数
            totalItemsSpan.textContent = totalItems;

            // 禁用/启用上一页、下一页按钮
            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= totalPages;

            // 设置按钮点击事件
            prevPageBtn.onclick = () => updatePendingApprovalList(currentPage - 1);
            nextPageBtn.onclick = () => updatePendingApprovalList(currentPage + 1);

            // 生成页码按钮
            pageNumbersContainer.innerHTML = '';

            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            // 调整起始页，确保始终显示5个页码（如果有足够的页数）
            if (endPage - startPage < 4 && totalPages > 5) {
                startPage = Math.max(1, endPage - 4);
            }

            // 添加第一页按钮（如果不在显示范围内）
            if (startPage > 1) {
                const firstPageBtn = document.createElement('button');
                firstPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
                firstPageBtn.textContent = '1';
                firstPageBtn.onclick = () => updatePendingApprovalList(1);
                pageNumbersContainer.appendChild(firstPageBtn);

                // 添加省略号（如果第一页和起始页之间有间隔）
                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-2 py-1';
                    ellipsis.textContent = '...';
                    pageNumbersContainer.appendChild(ellipsis);
                }
            }

            // 添加页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `px-3 py-1 border rounded-md ${i === currentPage ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => updatePendingApprovalList(i);
                pageNumbersContainer.appendChild(pageBtn);
            }

            // 添加最后一页按钮（如果不在显示范围内）
            if (endPage < totalPages) {
                // 添加省略号（如果结束页和最后一页之间有间隔）
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-2 py-1';
                    ellipsis.textContent = '...';
                    pageNumbersContainer.appendChild(ellipsis);
                }

                const lastPageBtn = document.createElement('button');
                lastPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
                lastPageBtn.textContent = totalPages;
                lastPageBtn.onclick = () => updatePendingApprovalList(totalPages);
                pageNumbersContainer.appendChild(lastPageBtn);
            }
        }

        // 全局变量，用于跟踪当前显示的卡片索引
        let currentCardIndex = 0;
        let pendingCards = [];

        // 更新移动端卡片视图
        function updatePendingCards(apps) {
            // 更新全局变量
            pendingCards = [...apps];

            // 重置当前卡片索引
            if (pendingCards.length > 0) {
                // 如果当前索引超出范围，重置为0
                if (currentCardIndex >= pendingCards.length) {
                    currentCardIndex = 0;
                }
            } else {
                currentCardIndex = 0;
            }

            // 更新卡片计数
            document.getElementById('totalCards').textContent = pendingCards.length;
            document.getElementById('currentCardIndex').textContent = pendingCards.length > 0 ? currentCardIndex + 1 : 0;

            // 渲染当前卡片
            renderCurrentCard();

            // 更新卡片导航按钮状态
            updateCardNavButtons();
        }

        // 渲染当前卡片
        function renderCurrentCard() {
            const cardContainer = document.getElementById('pendingCardContainer');

            if (pendingCards.length === 0) {
                cardContainer.innerHTML = `
                    <div class="text-center py-8">
                        <p class="text-green-600 font-semibold text-lg">您已完成所有审核，请休息一下吧</p>
                        <div class="mt-4">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto text-green-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                `;
                return;
            }

            if (currentCardIndex >= pendingCards.length) {
                currentCardIndex = pendingCards.length - 1;
            }

            const app = pendingCards[currentCardIndex];

            // 更新当前卡片索引显示
            document.getElementById('currentCardIndex').textContent = currentCardIndex + 1;

            // 生成卡片HTML
            cardContainer.innerHTML = `
                <div class="pending-card">
                    <div class="pending-card-header">
                        <div class="flex justify-between items-center">
                            <span class="font-semibold">${sanitizeInput(app.applicant)}</span>
                            <span class="px-2 py-1 rounded text-xs ${getPriorityClass(app.priority)}">${priorityMap[app.priority] || app.priority}</span>
                        </div>
                    </div>
                    <div class="pending-card-body">
                        ${app.applicationCode ? `
                        <div class="pending-card-item">
                            <span class="pending-card-label">申请编号</span>
                            <span class="pending-card-value">${app.applicationCode}</span>
                        </div>
                        ` : ''}
                        <div class="pending-card-item">
                            <span class="pending-card-label">申请部门</span>
                            <span class="pending-card-value">${sanitizeInput(app.department)}</span>
                        </div>
                        <div class="pending-card-item">
                            <span class="pending-card-label">申请日期</span>
                            <span class="pending-card-value">${app.date}</span>
                        </div>
                        <div class="pending-card-item">
                            <span class="pending-card-label">申请内容</span>
                            <div class="pending-card-content cursor-pointer" onclick="showContentModal('${escapeJsString(app.content)}')">
                                <div style="display: -webkit-box; -webkit-line-clamp: 4; -webkit-box-orient: vertical; overflow: hidden;">
                                    ${sanitizeInput(app.content.length > 100 ? app.content.substring(0, 100) + '...' : app.content).replace(/\n/g, '<br>')}
                                </div>
                            </div>
                        </div>
                        <div class="pending-card-item">
                            <span class="pending-card-label">当前状态</span>
                            <span class="pending-card-value">${app.status}</span>
                            ${app.status === '待厂长审核' && app.approvals && app.approvals.directors ?
                                `<div class="text-xs text-blue-600 mt-1">待审核厂长：${Object.entries(app.approvals.directors)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([director, _]) => sanitizeInput(director))
                                    .join('、') || '无'}</div>`
                                : ''}
                            ${app.status === '待经理审批' && app.approvals && app.approvals.managers ?
                                `<div class="text-xs text-blue-600 mt-1">待审核经理：${Object.entries(app.approvals.managers)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([manager, _]) => sanitizeInput(manager))
                                    .join('、') || '无'}</div>`
                                : ''}
                        </div>
                    </div>
                    <div class="pending-card-footer">
                        <div class="w-full flex justify-center">
                            <button onclick="viewDetail(${app.id})" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">审核申请</button>
                        </div>
                    </div>
                </div>
            `;
        }

        // 更新卡片导航按钮状态
        function updateCardNavButtons() {
            const prevBtn = document.getElementById('prevCardBtn');
            const nextBtn = document.getElementById('nextCardBtn');

            prevBtn.disabled = currentCardIndex <= 0 || pendingCards.length === 0;
            nextBtn.disabled = currentCardIndex >= pendingCards.length - 1 || pendingCards.length === 0;
        }

        // 显示上一个卡片
        function showPrevCard() {
            if (currentCardIndex > 0) {
                currentCardIndex--;
                renderCurrentCard();
                updateCardNavButtons();
            }
        }

        // 显示下一个卡片
        function showNextCard() {
            if (currentCardIndex < pendingCards.length - 1) {
                currentCardIndex++;
                renderCurrentCard();
                updateCardNavButtons();
            }
        }

        // 更新待审核数量
        function updatePendingCount(count) {
            // 更新待审核按钮上的数字
            const pendingCountElements = [
                document.getElementById('sidePendingCount'),
                document.getElementById('topPendingCount'),
                document.getElementById('pcPendingCount')
            ];

            pendingCountElements.forEach(element => {
                if (element) {
                    if (count === undefined) {
                        // 如果没有提供count参数，重新计算
                        const pendingApps = applications.filter(app => {
                            const needsApproval = needsApprovalByCurrentUser(app.status);

                            // 对于厂长和经理，还需要检查是否是指定的审批人
                            if (needsApproval) {
                                if (currentRole === 'director') {
                                    return app.approvals.directors &&
                                           app.approvals.directors[currentUser] &&
                                           app.approvals.directors[currentUser].status === 'pending';
                                } else if (currentRole === 'manager') {
                                    return app.approvals.managers &&
                                           app.approvals.managers[currentUser] &&
                                           app.approvals.managers[currentUser].status === 'pending';
                                }
                                return true; // 总监、CEO、管理员和只读用户不需要额外检查
                            }
                            return false;
                        });
                        count = pendingApps.length;
                    }

                    element.textContent = count;
                    if (count > 0) {
                        element.classList.remove('hidden');
                    } else {
                        element.classList.add('hidden');
                    }
                }
            });
        }

        // 判断当前用户是否需要审核该状态的申请
        function needsApprovalByCurrentUser(status) {
            if (currentRole === 'admin' || currentRole === 'readonly') {
                // 管理员和只读用户可以看到所有待审核的申请
                return status.includes('待');
            } else if (currentRole === 'director') {
                return status === '待厂长审核';
            } else if (currentRole === 'chief') {
                return status === '待总监审批';
            } else if (currentRole === 'manager') {
                return status === '待经理审批';
            } else if (currentRole === 'ceo') {
                return status === '待CEO审批';
            }
            return false;
        }

        // 搜索已审核申请
        function searchApprovedApplications() {
            const searchField = document.getElementById('approvedSearchField').value;
            const searchInput = document.getElementById('approvedSearchInput').value.toLowerCase();
            const range = document.getElementById('approvedTimeRange').value;
            const statusFilter = document.getElementById('approvedStatusFilter').value;

            if (!searchInput.trim()) {
                updateApprovedList(1); // 重置到第一页
                return;
            }

            const filteredApps = applications.filter(app => {
                // 先按时间范围筛选
                if (!isInRange(app.date, range)) return false;

                // 按状态筛选
                if (statusFilter !== 'all') {
                    const isApproved = app.status === '已通过' && app.approvals &&
                        ((app.approvals.chief && app.approvals.chief.status === 'approved') ||
                         (app.approvals.managers && Object.values(app.approvals.managers).some(m => m.status === 'approved')));

                    const isRejected = app.status === '已拒绝' && app.approvals &&
                        ((app.approvals.chief && app.approvals.chief.status === 'rejected') ||
                         (app.approvals.managers && Object.values(app.approvals.managers).some(m => m.status === 'rejected')));

                    if (statusFilter === 'approved' && !isApproved) return false;
                    if (statusFilter === 'rejected' && !isRejected) return false;
                }

                // 按角色筛选
                // 根据用户角色筛选已审核的申请
                if (currentRole === 'admin' || currentRole === 'readonly') {
                    // 管理员和只读用户可以看到所有已审核的申请
                    if (!(app.status === '已通过' || app.status === '已拒绝')) return false;
                } else if (currentRole === 'director') {
                    // 厂长只能看到自己已审核的申请
                    if (!(app.approvals && app.approvals.directors && app.approvals.directors[currentUser] &&
                        (app.approvals.directors[currentUser].status === 'approved' || app.approvals.directors[currentUser].status === 'rejected'))) return false;
                } else if (currentRole === 'chief') {
                    // 总监只能看到自己已审核的申请
                    if (!(app.approvals && app.approvals.chief &&
                        (app.approvals.chief.status === 'approved' || app.approvals.chief.status === 'rejected'))) return false;
                } else if (currentRole === 'manager') {
                    // 经理只能看到自己已审核的申请
                    if (!(app.approvals && app.approvals.managers && app.approvals.managers[currentUser] &&
                        (app.approvals.managers[currentUser].status === 'approved' || app.approvals.managers[currentUser].status === 'rejected'))) return false;
                } else if (currentRole === 'ceo') {
                    // CEO能看到自己作为CEO已审核的申请，以及之前作为经理审批过的申请
                    const ceoApproved = app.approvals && app.approvals.ceo &&
                                       (app.approvals.ceo.status === 'approved' || app.approvals.ceo.status === 'rejected');
                    const managerApproved = app.approvals && app.approvals.managers && app.approvals.managers[currentUser] &&
                                          (app.approvals.managers[currentUser].status === 'approved' || app.approvals.managers[currentUser].status === 'rejected');
                    if (!(ceoApproved || managerApproved)) return false;
                } else if (currentRole === 'user') {
                    // 普通用户只能看到自己的已完成申请
                    if (!(app.username === currentUser && (app.status === '已通过' || app.status === '已拒绝'))) return false;
                }

                // 按搜索字段筛选
                if (searchField === 'applicant') {
                    return app.applicant.toLowerCase().includes(searchInput);
                } else if (searchField === 'content') {
                    return app.content.toLowerCase().includes(searchInput);
                }
                return false;
            });

            // 按时间倒序排列
            const sortedApps = filteredApps.sort((a, b) => new Date(b.date) - new Date(a.date));

            // 分页处理
            const pageSize = 10;
            const totalItems = sortedApps.length;
            const totalPages = Math.ceil(totalItems / pageSize);
            const currentPage = 1; // 搜索结果从第一页开始显示

            // 获取当前页的数据
            const startIndex = 0;
            const endIndex = Math.min(pageSize, totalItems);
            const currentPageData = sortedApps.slice(startIndex, endIndex);

            // 渲染搜索结果
            const tbody = document.getElementById('approvedList');
            renderApprovedList(currentPageData, tbody);

            // 更新分页控件
            updateApprovedPagination(currentPage, totalPages, totalItems);
        }

        // 更新已审核列表
        function updateApprovedList(page = 1) {
            const range = document.getElementById('approvedTimeRange').value;
            const statusFilter = document.getElementById('approvedStatusFilter').value;

            // 筛选出当前用户已审核的申请
            let approvedApps = applications.filter(app => {
                // 首先检查是否在选定的时间范围内
                if (!isInRange(app.date, range)) {
                    return false;
                }

                // 然后根据状态过滤
                if (statusFilter !== 'all') {
                    if (statusFilter === 'approved' && !app.status.includes('通过')) {
                        return false;
                    }
                    if (statusFilter === 'rejected' && !app.status.includes('拒绝')) {
                        return false;
                    }
                }

                // 根据用户角色筛选已审核的申请
                if (currentRole === 'admin' || currentRole === 'readonly') {
                    // 管理员和只读用户可以看到所有已审核的申请
                    return app.status === '已通过' || app.status === '已拒绝';
                } else if (currentRole === 'director') {
                    // 厂长只能看到自己已审核的申请
                    return app.approvals &&
                           app.approvals.directors &&
                           app.approvals.directors[currentUser] &&
                           (app.approvals.directors[currentUser].status === 'approved' ||
                            app.approvals.directors[currentUser].status === 'rejected');
                } else if (currentRole === 'chief') {
                    // 总监只能看到自己已审核的申请
                    return app.approvals &&
                           app.approvals.chief &&
                           (app.approvals.chief.status === 'approved' ||
                            app.approvals.chief.status === 'rejected');
                } else if (currentRole === 'manager') {
                    // 经理只能看到自己已审核的申请
                    return app.approvals &&
                           app.approvals.managers &&
                           app.approvals.managers[currentUser] &&
                           (app.approvals.managers[currentUser].status === 'approved' ||
                            app.approvals.managers[currentUser].status === 'rejected');
                } else if (currentRole === 'ceo') {
                    // CEO能看到自己作为CEO已审核的申请，以及之前作为经理审批过的申请
                    const ceoApproved = app.approvals &&
                                       app.approvals.ceo &&
                                       (app.approvals.ceo.status === 'approved' ||
                                        app.approvals.ceo.status === 'rejected');

                    const managerApproved = app.approvals &&
                                          app.approvals.managers &&
                                          app.approvals.managers[currentUser] &&
                                          (app.approvals.managers[currentUser].status === 'approved' ||
                                           app.approvals.managers[currentUser].status === 'rejected');

                    return ceoApproved || managerApproved;
                } else if (currentRole === 'user') {
                    // 普通用户只能看到自己的已完成申请
                    return app.username === currentUser &&
                          (app.status === '已通过' || app.status === '已拒绝');
                }

                return false;
            });

            // 按日期排序（从新到旧）
            const sortedApps = approvedApps.sort((a, b) => new Date(b.date) - new Date(a.date));

            // 分页处理
            const pageSize = 10; // 每页显示10条记录
            const totalItems = sortedApps.length;
            const totalPages = Math.ceil(totalItems / pageSize);

            // 确保当前页在有效范围内
            page = Math.max(1, Math.min(page, totalPages || 1));

            // 获取当前页的数据
            const startIndex = (page - 1) * pageSize;
            const endIndex = Math.min(startIndex + pageSize, totalItems);
            const currentPageData = sortedApps.slice(startIndex, endIndex);

            // 渲染当前页数据
            const tbody = document.getElementById('approvedList');
            if (currentPageData.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="px-6 py-4 text-center text-gray-500">暂无数据</td></tr>';

                // 更新分页控件
                updateApprovedPagination(page, totalPages, totalItems);
                return;
            }

            renderApprovedList(currentPageData, tbody);

            // 更新分页控件
            updateApprovedPagination(page, totalPages, totalItems);
        }

        // 更新已审核分页控件
        function updateApprovedPagination(currentPage, totalPages, totalItems) {
            const prevPageBtn = document.getElementById('approvedPrevPage');
            const nextPageBtn = document.getElementById('approvedNextPage');
            const pageNumbersContainer = document.getElementById('approvedPageNumbers');
            const totalItemsSpan = document.getElementById('approvedTotalItems');

            // 更新总记录数
            totalItemsSpan.textContent = totalItems;

            // 禁用/启用上一页、下一页按钮
            prevPageBtn.disabled = currentPage <= 1;
            nextPageBtn.disabled = currentPage >= totalPages;

            // 设置按钮点击事件
            prevPageBtn.onclick = () => updateApprovedList(currentPage - 1);
            nextPageBtn.onclick = () => updateApprovedList(currentPage + 1);

            // 生成页码按钮
            pageNumbersContainer.innerHTML = '';

            // 确定要显示的页码范围
            let startPage = Math.max(1, currentPage - 2);
            let endPage = Math.min(totalPages, startPage + 4);

            // 调整起始页，确保始终显示5个页码（如果有足够的页数）
            if (endPage - startPage < 4 && totalPages > 5) {
                startPage = Math.max(1, endPage - 4);
            }

            // 添加第一页按钮（如果不在显示范围内）
            if (startPage > 1) {
                const firstPageBtn = document.createElement('button');
                firstPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
                firstPageBtn.textContent = '1';
                firstPageBtn.onclick = () => updateApprovedList(1);
                pageNumbersContainer.appendChild(firstPageBtn);

                // 添加省略号（如果第一页和起始页之间有间隔）
                if (startPage > 2) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-2 py-1';
                    ellipsis.textContent = '...';
                    pageNumbersContainer.appendChild(ellipsis);
                }
            }

            // 添加页码按钮
            for (let i = startPage; i <= endPage; i++) {
                const pageBtn = document.createElement('button');
                pageBtn.className = `px-3 py-1 border rounded-md ${i === currentPage ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`;
                pageBtn.textContent = i;
                pageBtn.onclick = () => updateApprovedList(i);
                pageNumbersContainer.appendChild(pageBtn);
            }

            // 添加最后一页按钮（如果不在显示范围内）
            if (endPage < totalPages) {
                // 添加省略号（如果结束页和最后一页之间有间隔）
                if (endPage < totalPages - 1) {
                    const ellipsis = document.createElement('span');
                    ellipsis.className = 'px-2 py-1';
                    ellipsis.textContent = '...';
                    pageNumbersContainer.appendChild(ellipsis);
                }

                const lastPageBtn = document.createElement('button');
                lastPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
                lastPageBtn.textContent = totalPages;
                lastPageBtn.onclick = () => updateApprovedList(totalPages);
                pageNumbersContainer.appendChild(lastPageBtn);
            }
        }

        // 更新导航按钮显示
        function updateNavButtons() {
            // 根据用户角色显示不同的导航按钮
            // 移动端侧边栏按钮
            const sidePendingApprovalBtn = document.getElementById('sidePendingApprovalBtn');
            const sideApprovedBtn = document.getElementById('sideApprovedBtn');
            const sideSystemSettingsBtn = document.getElementById('sideSystemSettingsBtn');
            const sideManageUsersBtn = document.getElementById('sideManageUsersBtn');

            // PC端侧边栏按钮
            const pcPendingApprovalBtn = document.getElementById('pcPendingApprovalBtn');
            const pcApprovedBtn = document.getElementById('pcApprovedBtn');
            const pcSystemSettingsBtn = document.getElementById('pcSystemSettingsBtn');
            const pcManageUsersBtn = document.getElementById('pcManageUsersBtn');

            // 顶部导航按钮（保留兼容性）
            const topPendingApprovalBtn = document.getElementById('topPendingApprovalBtn');
            const topApprovedBtn = document.getElementById('topApprovedBtn');
            const topSystemSettingsBtn = document.getElementById('topSystemSettingsBtn');
            const topManageUsersBtn = document.getElementById('topManageUsersBtn');

            // 新建申请和申请记录按钮
            const newAppBtn = document.querySelector('.nav-btn[data-section="new"]');
            const historyBtn = document.querySelector('.nav-btn[data-section="history"]');
            const sideNewAppBtn = document.querySelector('.side-nav-btn[data-section="new"]');
            const sideHistoryBtn = document.querySelector('.side-nav-btn[data-section="history"]');
            const pcNewAppBtn = document.querySelector('.pc-nav-btn[data-section="new"]');
            const pcHistoryBtn = document.querySelector('.pc-nav-btn[data-section="history"]');

            // 总监、经理、CEO和只读用户不显示新建申请按钮
            if (['chief', 'manager', 'ceo', 'readonly'].includes(currentRole)) {
                if (newAppBtn) newAppBtn.classList.add('hidden');
                if (sideNewAppBtn) sideNewAppBtn.classList.add('hidden');
                if (pcNewAppBtn) pcNewAppBtn.classList.add('hidden');
            } else {
                if (newAppBtn) newAppBtn.classList.remove('hidden');
                if (sideNewAppBtn) sideNewAppBtn.classList.remove('hidden');
                if (pcNewAppBtn) pcNewAppBtn.classList.remove('hidden');
            }

            // 总监、经理和CEO不显示申请记录按钮
            if (['chief', 'manager', 'ceo'].includes(currentRole)) {
                if (historyBtn) historyBtn.classList.add('hidden');
                if (sideHistoryBtn) sideHistoryBtn.classList.add('hidden');
                if (pcHistoryBtn) pcHistoryBtn.classList.add('hidden');
            } else {
                if (historyBtn) historyBtn.classList.remove('hidden');
                if (sideHistoryBtn) sideHistoryBtn.classList.remove('hidden');
                if (pcHistoryBtn) pcHistoryBtn.classList.remove('hidden');
            }

            if (['director', 'chief', 'manager', 'ceo', 'admin', 'readonly'].includes(currentRole)) {
                // 移动端侧边栏按钮
                if (sidePendingApprovalBtn) sidePendingApprovalBtn.classList.remove('hidden');
                if (sideApprovedBtn) sideApprovedBtn.classList.remove('hidden');

                // PC端侧边栏按钮
                if (pcPendingApprovalBtn) pcPendingApprovalBtn.classList.remove('hidden');
                if (pcApprovedBtn) pcApprovedBtn.classList.remove('hidden');

                // 顶部导航按钮（保留兼容性）
                if (topPendingApprovalBtn) topPendingApprovalBtn.classList.remove('hidden');
                if (topApprovedBtn) topApprovedBtn.classList.remove('hidden');
            } else {
                // 移动端侧边栏按钮
                if (sidePendingApprovalBtn) sidePendingApprovalBtn.classList.add('hidden');
                if (sideApprovedBtn) sideApprovedBtn.classList.add('hidden');

                // PC端侧边栏按钮
                if (pcPendingApprovalBtn) pcPendingApprovalBtn.classList.add('hidden');
                if (pcApprovedBtn) pcApprovedBtn.classList.add('hidden');

                // 顶部导航按钮（保留兼容性）
                if (topPendingApprovalBtn) topPendingApprovalBtn.classList.add('hidden');
                if (topApprovedBtn) topApprovedBtn.classList.add('hidden');
            }

            if (currentRole === 'admin') {
                // 移动端侧边栏按钮
                if (sideSystemSettingsBtn) sideSystemSettingsBtn.classList.remove('hidden');
                if (sideManageUsersBtn) sideManageUsersBtn.classList.remove('hidden');

                // PC端侧边栏按钮
                if (pcSystemSettingsBtn) pcSystemSettingsBtn.classList.remove('hidden');
                if (pcManageUsersBtn) pcManageUsersBtn.classList.remove('hidden');

                // 顶部导航按钮（保留兼容性）
                if (topSystemSettingsBtn) topSystemSettingsBtn.classList.remove('hidden');
                if (topManageUsersBtn) topManageUsersBtn.classList.remove('hidden');
            } else {
                // 移动端侧边栏按钮
                if (sideSystemSettingsBtn) sideSystemSettingsBtn.classList.add('hidden');
                if (sideManageUsersBtn) sideManageUsersBtn.classList.add('hidden');

                // PC端侧边栏按钮
                if (pcSystemSettingsBtn) pcSystemSettingsBtn.classList.add('hidden');
                if (pcManageUsersBtn) pcManageUsersBtn.classList.add('hidden');

                // 顶部导航按钮（保留兼容性）
                if (topSystemSettingsBtn) topSystemSettingsBtn.classList.add('hidden');
                if (topManageUsersBtn) topManageUsersBtn.classList.add('hidden');
            }
        }

        // 更新已选择的经理数量显示
        function updateSelectedManagersCount(appId) {
            const container = document.getElementById('detail-managers-list');
            const countElement = document.getElementById('detail-selected-managers-count');
            if (container && countElement) {
                const selectedCount = container.querySelectorAll('.manager-checkbox:checked').length;
                countElement.textContent = `已选择 ${selectedCount} 位经理`;

                // 移除可能的绿色文本样式
                countElement.classList.remove('text-green-600');

                // 重置确认状态
                const confirmStatus = document.getElementById('detail-managers-confirm-status');
                if (confirmStatus) {
                    confirmStatus.classList.add('hidden');
                }

                // 标记为未确认
                container.dataset.confirmed = 'false';
            }
        }

        // 确认选择的经理
        function confirmSelectedManagers(appId) {
            const container = document.getElementById('detail-managers-list');
            const confirmStatus = document.getElementById('detail-managers-confirm-status');
            const countElement = document.getElementById('detail-selected-managers-count');

            if (container && confirmStatus) {
                const selectedManagers = getSelectedManagers(container);

                if (selectedManagers.length === 0) {
                    if (!confirm('您没有选择任何经理，申请将在您审批通过后流转给CEO进行最终审批。是否确认？')) {
                        return;
                    }

                    // 显示确认消息
                    alert('已确认不选择任何经理，申请将在您审批通过后流转给CEO进行最终审批');

                    // 更新计数显示
                    if (countElement) {
                        countElement.textContent = '已选择 0 位经理 (已确认)';
                        countElement.classList.add('text-green-600');
                    }

                    // 显示确认状态
                    confirmStatus.classList.remove('hidden');
                    return;
                }

                // 获取经理名称列表用于显示
                const managerNames = selectedManagers.map(managerId => {
                    const managerLabel = container.querySelector(`label[for="manager_${appId}_${managerId}"]`);
                    return managerLabel ? managerLabel.textContent.trim() : managerId;
                });

                // 显示确认消息
                alert(`已确认选择以下经理进行审批：\n${managerNames.join('\n')}`);

                // 更新计数显示
                if (countElement) {
                    countElement.textContent = `已选择 ${selectedManagers.length} 位经理 (已确认)`;
                    countElement.classList.add('text-green-600');
                }

                // 标记为已确认
                container.dataset.confirmed = 'true';
                confirmStatus.classList.remove('hidden');
            }
        }

        // 切换菜单显示/隐藏
        function toggleMenu() {
            const sideMenu = document.getElementById('sideMenu');
            const menuOverlay = document.getElementById('menuOverlay');

            if (sideMenu.classList.contains('-translate-x-full')) {
                // 显示菜单
                sideMenu.classList.remove('-translate-x-full');
                menuOverlay.classList.remove('hidden');
                document.body.classList.add('overflow-hidden'); // 防止背景滚动
            } else {
                // 隐藏菜单
                sideMenu.classList.add('-translate-x-full');
                menuOverlay.classList.add('hidden');
                document.body.classList.remove('overflow-hidden');
            }
        }

        // 仅在移动设备上切换菜单
        function toggleMenuMobile() {
            // 检查是否为移动设备
            if (window.innerWidth < 768) {
                toggleMenu();
            }
        }

        // 预览申请书模板
        async function previewApplicationTemplate(appId) {
            // 如果提供了appId，表示从详情页面调用
            if (appId) {
                const app = applications.find(a => a.id === appId);
                if (!app) {
                    alert('找不到申请数据');
                    return;
                }

                // 显示加载指示器
                const loadingIndicator = document.createElement('div');
                loadingIndicator.id = 'templateLoadingIndicator';
                loadingIndicator.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
                loadingIndicator.textContent = '正在加载签名数据，请稍候...';
                document.body.appendChild(loadingIndicator);

                try {
                    // 直接使用从应用程序中提取的签名信息生成模板
                    await generateApplicationTemplateWithApp(app);
                } catch (error) {
                    console.error('生成申请模板出错:', error);
                    alert('生成申请模板时出错，请重试');
                } finally {
                    // 移除加载指示器
                    const indicator = document.getElementById('templateLoadingIndicator');
                    if (indicator) {
                        indicator.remove();
                    }
                }
            } else {
                // 从新申请表单获取数据
                const applicant = document.getElementById('applicant').value;
                const department = document.getElementById('department').value;
                const date = document.getElementById('applyDate').value;
                const content = document.getElementById('content').value;

                // 严格验证必填字段
                const previewValidationResult = validatePreviewData(applicant, department, date, content);
                if (!previewValidationResult.isValid) {
                    showValidationError(previewValidationResult.errors);
                    return;
                }

                // 创建申请对象
                const application = {
                    applicant: currentUser, // 使用当前用户作为申请人
                    department: department,
                    date: date,
                    content: content,
                    approvals: {
                        directors: {},
                        chief: {},
                        managers: {}
                    }
                };

                // 生成并显示申请书模板
                generateApplicationTemplate(application);
            }
        }

        // 使用应用数据生成申请书模板
        async function generateApplicationTemplateWithApp(app) {
            // 强制重新加载用户数据和提取签名，确保获取最新的签名信息
            console.log('=== 开始生成申请书模板 ===');
            console.log('申请编号:', app.applicationCode);
            console.log('申请状态:', app.status);

            // 强制重新加载用户数据
            await loadAllUsers();

            // 强制重新提取签名信息
            extractSignaturesFromApplications();

            console.log('生成申请书模板，当前用户签名数据:', allUsers);
            console.log('CEO审批记录:', app.approvals?.ceo);

            // 创建包含审批信息的申请对象
            const application = {
                applicant: app.applicant,
                department: app.department,
                date: app.date,
                content: app.content,
                approvals: {
                    directors: {},
                    chief: {},
                    managers: {},
                    ceo: {}
                }
            };

            // 处理厂长审批信息
            if (app.approvals && app.approvals.directors) {
                Object.entries(app.approvals.directors).forEach(([username, approval]) => {
                    if (approval.status === 'approved' || approval.status === 'rejected') {
                        // 获取用户信息以获取电子签名
                        const user = allUsers ? allUsers.find(u => u.username === username) : null;
                        const signature = user && user.signature ? user.signature :
                                         approval.signature ? approval.signature : null;

                        console.log(`厂长 ${username} 的签名信息:`, signature ? '有签名' : '无签名');

                        application.approvals.directors[username] = {
                            status: approval.status,
                            comment: approval.comment || (approval.status === 'approved' ? '同意' : '不同意'),
                            date: approval.date || new Date().toISOString(),
                            approverName: user ? (user.displayName || username) : username,
                            signature: signature
                        };
                    }
                });
            }

            // 处理总监审批信息
            if (app.approvals && app.approvals.chief && (app.approvals.chief.status === 'approved' || app.approvals.chief.status === 'rejected')) {
                // 找到总监用户
                const chiefUser = allUsers ? allUsers.find(u => u.username === app.approvals.chief.username || u.role === 'chief') : null;
                const signature = chiefUser && chiefUser.signature ? chiefUser.signature :
                                 app.approvals.chief.signature ? app.approvals.chief.signature : null;

                console.log(`总监的签名信息:`, signature ? '有签名' : '无签名');

                application.approvals.chief = {
                    status: app.approvals.chief.status,
                    comment: app.approvals.chief.comment || (app.approvals.chief.status === 'approved' ? '同意' : '不同意'),
                    date: app.approvals.chief.date || new Date().toISOString(),
                    approverName: chiefUser ? (chiefUser.displayName || chiefUser.username) : '总监',
                    signature: signature
                };
            }

            // 处理经理审批信息
            if (app.approvals && app.approvals.managers) {
                Object.entries(app.approvals.managers).forEach(([username, approval]) => {
                    if (approval.status === 'approved' || approval.status === 'rejected') {
                        // 获取用户信息以获取电子签名
                        const user = allUsers ? allUsers.find(u => u.username === username) : null;
                        const signature = user && user.signature ? user.signature :
                                         approval.signature ? approval.signature : null;

                        console.log(`经理 ${username} 的签名信息:`, signature ? '有签名' : '无签名');

                        application.approvals.managers[username] = {
                            status: approval.status,
                            comment: approval.comment || (approval.status === 'approved' ? '同意' : '不同意'),
                            date: approval.date || new Date().toISOString(),
                            approverName: user ? (user.displayName || username) : username,
                            signature: signature
                        };
                    }
                });
            }

            // 处理CEO审批信息 - 增强版，确保历史数据也能正确处理
            if (app.approvals && app.approvals.ceo) {
                const ceoApproval = app.approvals.ceo;
                if (ceoApproval.status === 'approved' || ceoApproval.status === 'rejected') {
                    // 获取CEO用户名，兼容历史数据
                    const ceoUsername = ceoApproval.approverUsername ||
                                       ceoApproval.username ||
                                       'ceo';

                    // 多种方式查找CEO用户信息以获取电子签名
                    const user = allUsers ? allUsers.find(u =>
                        u.role === 'ceo' ||
                        u.username === ceoUsername ||
                        (u.username && u.username.toLowerCase().includes('ceo')) ||
                        (ceoUsername !== 'ceo' && u.username === ceoUsername)
                    ) : null;

                    // 彻底修复：直接使用审批记录中的签名，不依赖用户数据
                    const signature = ceoApproval.signature || (user && user.signature) || null;

                    console.log(`CEO 的签名信息:`, signature ? '有签名' : '无签名');
                    console.log(`CEO 用户名: ${ceoUsername}, 找到用户:`, user ? user.username : '未找到');
                    console.log(`CEO 审批记录详细信息:`, {
                        status: ceoApproval.status,
                        approverUsername: ceoApproval.approverUsername,
                        username: ceoApproval.username,
                        hasSignatureInRecord: !!ceoApproval.signature,
                        signatureLengthInRecord: ceoApproval.signature ? ceoApproval.signature.length : 0,
                        hasUserSignature: !!(user && user.signature),
                        finalSignature: !!signature,
                        finalSignatureLength: signature ? signature.length : 0
                    });

                    application.approvals.ceo = {
                        status: ceoApproval.status,
                        comment: ceoApproval.comment || (ceoApproval.status === 'approved' ? '同意' : '不同意'),
                        date: ceoApproval.date || new Date().toISOString(),
                        approverName: user ? (user.displayName || user.username || 'CEO') : (ceoUsername !== 'ceo' ? ceoUsername : 'CEO'),
                        signature: signature,
                        // 保留原始审批记录信息，便于调试
                        approverUsername: ceoApproval.approverUsername,
                        username: ceoApproval.username
                    };
                }
            }

            // 生成并显示申请书模板
            generateApplicationTemplate(application);
        }

        // 生成申请书模板
        function generateApplicationTemplate(application) {
            // 格式化日期
            const formattedDate = formatDate(application.date);

            // 获取审批信息
            let factoryApproval = '';
            let directorApproval = '';
            let managerApproval = '';

            // 处理厂长审批信息
            if (application.approvals && application.approvals.directors) {
                const directorsEntries = Object.entries(application.approvals.directors);
                if (directorsEntries.length > 0) {
                    factoryApproval = `<div class="approval-container">`;
                    directorsEntries.forEach(([username, approval]) => {
                        if (approval.status === 'approved' || approval.status === 'rejected') {
                            factoryApproval += `
                                <div class="single-approval">
                                    ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                                    <div class="signature-wrapper">
                                        ${approval.signature ?
                                            `<img src="${approval.signature}" alt="${approval.approverName || username}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                            `<div class="no-signature">无签名</div>`
                                        }
                                        <div class="approval-date">${formatDate(approval.date)}</div>
                                    </div>
                                </div>
                            `;
                        }
                    });
                    factoryApproval += `</div>`;
                }
            }

            // 处理总监审批信息
            if (application.approvals && application.approvals.chief) {
                const chiefApproval = application.approvals.chief;
                if (chiefApproval.status === 'approved' || chiefApproval.status === 'rejected') {
                    directorApproval = `
                        <div class="approval-container">
                            <div class="single-approval">
                                ${chiefApproval.comment ? `<div class="approval-comment">${sanitizeInput(chiefApproval.comment)}</div>` : ''}
                                <div class="signature-wrapper">
                                    ${chiefApproval.signature ?
                                        `<img src="${chiefApproval.signature}" alt="${chiefApproval.approverName || '总监'}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                        `<div class="no-signature">无签名</div>`
                                    }
                                    <div class="approval-date">${formatDate(chiefApproval.date)}</div>
                                </div>
                            </div>
                        </div>
                    `;
                }
            }

            // 处理经理审批信息（包含CEO签名）
            if (application.approvals && application.approvals.managers) {
                const managersEntries = Object.entries(application.approvals.managers);
                if (managersEntries.length > 0) {
                    managerApproval = `<div class="approval-container">`;
                    managersEntries.forEach(([username, approval]) => {
                        if (approval.status === 'approved' || approval.status === 'rejected') {
                            // 检查是否有角色变更信息
                            let roleChangeNote = '';
                            if (approval.originalRole && approval.roleChangedTo) {
                                roleChangeNote = `<div class="role-change-note" style="font-size: 10px; color: #666; margin-top: 2px;">注：${username} 已从${getRoleDisplayName(approval.originalRole)}变更为${getRoleDisplayName(approval.roleChangedTo)}</div>`;
                            }

                            managerApproval += `
                                <div class="single-approval">
                                    ${approval.comment ? `<div class="approval-comment">${sanitizeInput(approval.comment)}</div>` : ''}
                                    <div class="signature-wrapper">
                                        ${approval.signature ?
                                            `<img src="${approval.signature}" alt="${approval.approverName || username}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                            `<div class="no-signature">无签名</div>`
                                        }
                                        <div class="approval-date">${formatDate(approval.date)}</div>
                                        ${roleChangeNote}
                                    </div>
                                </div>
                            `;
                        }
                    });

                    // 添加CEO审批信息到经理核准区域 - 增强版，确保历史数据也能正确处理
                    if (application.approvals.ceo && (application.approvals.ceo.status === 'approved' || application.approvals.ceo.status === 'rejected')) {
                        const ceoApproval = application.approvals.ceo;

                        // 获取CEO用户名，兼容历史数据
                        const ceoUsername = ceoApproval.approverUsername ||
                                           ceoApproval.username ||
                                           'ceo';

                        // 多种方式查找CEO用户信息
                        const ceoUser = allUsers ? allUsers.find(u =>
                            u.role === 'ceo' ||
                            u.username === ceoUsername ||
                            (u.username && u.username.toLowerCase().includes('ceo')) ||
                            (ceoUsername !== 'ceo' && u.username === ceoUsername)
                        ) : null;

                        // 彻底修复：直接使用审批记录中的签名，不依赖用户数据
                        const ceoSignature = ceoApproval.signature || (ceoUser && ceoUser.signature) || null;

                        console.log('CEO的签名信息:', ceoSignature ? '有签名' : '无签名');
                        console.log('CEO用户查找结果:', ceoUser ? `找到用户: ${ceoUser.username}` : '未找到用户');
                        console.log('CEO签名来源:', ceoUser && ceoUser.signature ? '来自用户数据' : (ceoApproval.signature ? '来自审批记录' : '无签名'));
                        console.log('CEO审批记录详情:', {
                            status: ceoApproval.status,
                            approverUsername: ceoApproval.approverUsername,
                            username: ceoApproval.username,
                            hasSignature: !!ceoApproval.signature,
                            signatureLength: ceoApproval.signature ? ceoApproval.signature.length : 0
                        });
                        console.log('allUsers中的CEO用户:', allUsers ? allUsers.filter(u => u.role === 'ceo' || (u.username && u.username.toLowerCase().includes('ceo'))) : '无allUsers数据');
                        console.log('查找CEO用户的条件:', {
                            ceoUsername: ceoUsername,
                            allUsersCount: allUsers ? allUsers.length : 0,
                            ceoRoleUsers: allUsers ? allUsers.filter(u => u.role === 'ceo').map(u => u.username) : [],
                            usernameMatches: allUsers ? allUsers.filter(u => u.username === ceoUsername).map(u => u.username) : []
                        });

                        // 检查CEO是否有角色变更信息
                        let ceoRoleChangeNote = '';
                        if (ceoApproval.originalRole && ceoApproval.roleChangedTo) {
                            const approverName = ceoApproval.approverUsername || 'CEO';
                            ceoRoleChangeNote = `<div class="role-change-note" style="font-size: 10px; color: #666; margin-top: 2px;">注：${approverName} 已从${getRoleDisplayName(ceoApproval.originalRole)}变更为${getRoleDisplayName(ceoApproval.roleChangedTo)}</div>`;
                        }

                        managerApproval += `
                            <div class="single-approval">
                                ${ceoApproval.comment ? `<div class="approval-comment">${sanitizeInput(ceoApproval.comment)}</div>` : ''}
                                <div class="signature-wrapper">
                                    ${ceoSignature ?
                                        `<img src="${ceoSignature}" alt="${ceoUser ? (ceoUser.displayName || 'CEO') : 'CEO'}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                        `<div class="no-signature">无签名</div>`
                                    }
                                    <div class="approval-date">${formatDate(ceoApproval.date)}</div>
                                    ${ceoRoleChangeNote}
                                </div>
                            </div>
                        `;
                    }

                    managerApproval += `</div>`;
                }
            }

            // 彻底修复：无论是否有经理审批，只要有CEO审批就要显示CEO签名
            if (application.approvals && application.approvals.ceo && (application.approvals.ceo.status === 'approved' || application.approvals.ceo.status === 'rejected')) {
                // 检查是否已经有经理审批内容
                const hasManagerApproval = application.approvals.managers && Object.keys(application.approvals.managers).length > 0;

                if (!hasManagerApproval) {
                    // 如果没有经理审批，CEO签名显示在经理核准区域
                    const ceoApproval = application.approvals.ceo;

                    // 获取CEO用户名，兼容历史数据
                    const ceoUsername = ceoApproval.approverUsername ||
                                       ceoApproval.username ||
                                       'ceo';

                    console.log('=== 独立CEO审批签名处理 ===');
                    console.log('CEO用户名:', ceoUsername);
                    console.log('CEO审批记录中的签名:', ceoApproval.signature ? '有签名' : '无签名');
                    console.log('CEO审批记录签名长度:', ceoApproval.signature ? ceoApproval.signature.length : 0);

                    // 多种方式查找CEO用户信息
                    const ceoUser = allUsers ? allUsers.find(u =>
                        u.role === 'ceo' ||
                        u.username === ceoUsername ||
                        (u.username && u.username.toLowerCase().includes('ceo')) ||
                        (ceoUsername !== 'ceo' && u.username === ceoUsername)
                    ) : null;

                    console.log('找到的CEO用户:', ceoUser ? ceoUser.username : '未找到');
                    console.log('CEO用户签名:', ceoUser && ceoUser.signature ? '有签名' : '无签名');

                    // 彻底修复：直接使用审批记录中的签名，不依赖用户数据
                    const ceoSignature = ceoApproval.signature || (ceoUser && ceoUser.signature) || null;

                    console.log('最终使用的CEO签名:', ceoSignature ? '有签名' : '无签名');
                    console.log('最终签名长度:', ceoSignature ? ceoSignature.length : 0);

                    // 检查CEO是否有角色变更信息
                    let ceoRoleChangeNote = '';
                    if (ceoApproval.originalRole && ceoApproval.roleChangedTo) {
                        const approverName = ceoApproval.approverUsername || 'CEO';
                        ceoRoleChangeNote = `<div class="role-change-note" style="font-size: 10px; color: #666; margin-top: 2px;">注：${approverName} 已从${getRoleDisplayName(ceoApproval.originalRole)}变更为${getRoleDisplayName(ceoApproval.roleChangedTo)}</div>`;
                    }

                    managerApproval = `
                        <div class="approval-container">
                            <div class="single-approval">
                                ${ceoApproval.comment ? `<div class="approval-comment">${sanitizeInput(ceoApproval.comment)}</div>` : ''}
                                <div class="signature-wrapper">
                                    ${ceoSignature ?
                                        `<img src="${ceoSignature}" alt="${ceoUser ? (ceoUser.displayName || 'CEO') : 'CEO'}的签名" class="signature-image" style="image-rendering: -webkit-optimize-contrast; filter: contrast(1.05);">` :
                                        `<div class="no-signature">无签名</div>`
                                    }
                                    <div class="approval-date">${formatDate(ceoApproval.date)}</div>
                                    ${ceoRoleChangeNote}
                                </div>
                            </div>
                        </div>
                    `;
                }
            }



            // 检测是否为移动设备
            const isMobile = window.innerWidth <= 768;
            const isSmallMobile = window.innerWidth <= 480;

            // 根据设备类型调整样式
            let mobileClass = '';
            if (isSmallMobile) {
                mobileClass = 'mobile-small';
            } else if (isMobile) {
                mobileClass = 'mobile';
            }

            const templateHTML = `
                <table class="application-template ${mobileClass}">
                    <tr>
                        <th colspan="4" class="title">申 请 书</th>
                    </tr>
                    <tr style="height: 5%;">
                        <td class="label">申请部门</td>
                        <td>${sanitizeInput(application.department)}</td>
                        <td class="label">申请日期</td>
                        <td>${formattedDate}</td>
                    </tr>
                    <tr>
                        <td class="label" rowspan="2">申请事由</td>
                        <td colspan="3" class="content">${sanitizeInput(application.content).replace(/\n/g, '<br>')}</td>
                    </tr>
                    <tr style="height: 5%;">
                        <td colspan="3" class="applicant">申请人: ${sanitizeInput(application.applicant)}</td>
                    </tr>
                    <tr>
                        <td class="label">厂长意见</td>
                        <td colspan="3" class="approval">${factoryApproval}</td>
                    </tr>
                    <tr>
                        <td class="label">总监意见</td>
                        <td colspan="3" class="approval">${directorApproval}</td>
                    </tr>
                    <tr>
                        <td class="label">经理核准</td>
                        <td colspan="3" class="approval">${managerApproval}</td>
                    </tr>
                </table>
            `;

            // 更新模板内容
            document.getElementById('applicationTemplateContent').innerHTML = templateHTML;

            // 显示模态框
            document.getElementById('applicationTemplateModal').classList.remove('hidden');

            // 调整表格高度以填满A4页面
            adjustTableHeight();

            // 调试信息：检查签名是否存在
            console.log('=== 生成的申请书模板中的签名信息 ===');
            if (application.approvals.directors) {
                Object.entries(application.approvals.directors).forEach(([username, approval]) => {
                    console.log(`厂长 ${username} 签名:`, approval.signature ? '有' : '无');
                });
            }
            if (application.approvals.chief) {
                console.log('总监签名:', application.approvals.chief.signature ? '有' : '无');
            }
            if (application.approvals.managers) {
                Object.entries(application.approvals.managers).forEach(([username, approval]) => {
                    console.log(`经理 ${username} 签名:`, approval.signature ? '有' : '无');
                });
            }
            if (application.approvals.ceo) {
                console.log('CEO签名:', application.approvals.ceo.signature ? '有' : '无');
                console.log('CEO签名数据长度:', application.approvals.ceo.signature ? application.approvals.ceo.signature.length : 0);
                console.log('CEO签名前50字符:', application.approvals.ceo.signature ? application.approvals.ceo.signature.substring(0, 50) + '...' : '无');
            }

            // 最终检查：如果CEO签名仍然没有显示，强制检查原始数据
            if (application.approvals.ceo && !application.approvals.ceo.signature) {
                console.log('=== CEO签名缺失，检查原始数据 ===');
                const originalApp = applications.find(a => a.applicationCode === application.applicationCode || a.id === application.id);
                if (originalApp && originalApp.approvals && originalApp.approvals.ceo && originalApp.approvals.ceo.signature) {
                    console.log('在原始数据中找到CEO签名，强制更新');
                    application.approvals.ceo.signature = originalApp.approvals.ceo.signature;
                    // 重新生成模板
                    generateApplicationTemplate(application);
                    return;
                }
            }
        }

        // 调整表格高度以填满A4页面
        function adjustTableHeight() {
            // 获取A4页面的高度
            const a4Page = document.getElementById('applicationTemplatePage');
            const a4Height = a4Page.clientHeight;

            // 获取表格
            const table = document.querySelector('.application-template');
            if (!table) return;

            // 设置表格高度为A4页面高度的90%，减少原来的95%，留出更多底部空间
            table.style.height = (a4Height * 0.90) + 'px';

            // 获取内容行和其他行
            const contentRow = table.querySelector('tr:nth-child(3)');
            const otherRows = Array.from(table.querySelectorAll('tr')).filter(row => row !== contentRow);

            // 计算其他行的总高度
            let otherRowsHeight = 0;
            otherRows.forEach(row => {
                otherRowsHeight += row.offsetHeight;
            });

            // 计算内容行应该的高度
            const contentHeight = (a4Height * 0.90) - otherRowsHeight;

            // 设置内容行的高度
            if (contentRow && contentHeight > 0) {
                contentRow.style.height = contentHeight + 'px';
                contentRow.querySelector('.content').style.height = contentHeight + 'px';
            }

            // 移动设备适配：确保在窗口大小变化时重新调整
            window.addEventListener('resize', debounce(function() {
                // 重新获取A4页面的高度
                const a4PageHeight = document.getElementById('applicationTemplatePage').clientHeight;

                // 重新设置表格高度
                if (table) {
                    table.style.height = (a4PageHeight * 0.90) + 'px';

                    // 重新计算其他行的总高度
                    let newOtherRowsHeight = 0;
                    otherRows.forEach(row => {
                        newOtherRowsHeight += row.offsetHeight;
                    });

                    // 重新计算内容行应该的高度
                    const newContentHeight = (a4PageHeight * 0.90) - newOtherRowsHeight;

                    // 重新设置内容行的高度
                    if (contentRow && newContentHeight > 0) {
                        contentRow.style.height = newContentHeight + 'px';
                        contentRow.querySelector('.content').style.height = newContentHeight + 'px';
                    }
                }
            }, 250));
        }

        // 防抖函数，避免频繁触发resize事件
        function debounce(func, wait) {
            let timeout;
            return function() {
                const context = this;
                const args = arguments;
                clearTimeout(timeout);
                timeout = setTimeout(() => {
                    func.apply(context, args);
                }, wait);
            };
        }

        // 关闭申请书模板
        function closeApplicationTemplate() {
            document.getElementById('applicationTemplateModal').classList.add('hidden');
        }

        // 下载申请书模板为PDF
        function downloadApplicationTemplate() {
            const element = document.getElementById('applicationTemplatePage');
            const filename = '申请书_' + new Date().toISOString().slice(0, 10) + '.pdf';

            // 显示加载提示
            const loadingMsg = document.createElement('div');
            loadingMsg.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
            loadingMsg.textContent = '正在生成PDF，请稍候...';
            document.body.appendChild(loadingMsg);

            // 先加载PDF库
            window.loadPdfLibraries().then(() => {
                // 使用html2canvas将元素转换为canvas
                html2canvas(element, {
                    scale: 2, // 提高清晰度
                    useCORS: true, // 允许加载跨域图片
                    logging: false,
                    allowTaint: true,
                    backgroundColor: '#ffffff'
                }).then(canvas => {
                    // 使用jsPDF将canvas转换为PDF
                    const { jsPDF } = window.jspdf;
                    const pdf = new jsPDF('p', 'mm', 'a4');

                    // 计算宽高比例
                    const imgData = canvas.toDataURL('image/png');
                    const pageWidth = pdf.internal.pageSize.getWidth();
                    const pageHeight = pdf.internal.pageSize.getHeight();
                    const canvasWidth = canvas.width;
                    const canvasHeight = canvas.height;
                    const ratio = Math.min(pageWidth / canvasWidth, pageHeight / canvasHeight);
                    const imgWidth = canvasWidth * ratio;
                    const imgHeight = canvasHeight * ratio;

                    // 添加图像到PDF，调整y坐标为-5mm，使内容上移
                    pdf.addImage(imgData, 'PNG', 0, -5, imgWidth, imgHeight);

                    // 下载PDF
                    pdf.save(filename);

                    // 移除加载提示
                    document.body.removeChild(loadingMsg);
                }).catch(error => {
                    console.error('生成PDF失败:', error);
                    alert('生成PDF失败，请重试');
                    document.body.removeChild(loadingMsg);
                });
            }).catch(error => {
                console.error('加载PDF库失败:', error);
                alert('加载PDF库失败，请检查网络连接后重试');
                document.body.removeChild(loadingMsg);
            });
        }

        // 打印申请书模板（保留但不使用）
        function printApplicationTemplate() {
            window.print();
        }

        // 格式化日期 (YYYY-MM-DD 转为 YYYY年MM月DD日)
        function formatDate(dateString) {
            const date = new Date(dateString);
            const year = date.getFullYear();
            const month = date.getMonth() + 1;
            const day = date.getDate();
            return `${year}年${month}月${day}日`;
        }

        // 模拟获取用户电子签名的接口
        // 在实际环境中，这应该是一个真实的后端API
        async function getUserSignature(username) {
            // 在实际应用中，这里应该是一个fetch请求到后端API
            // 这里我们模拟一个响应
            return new Promise((resolve) => {
                // 查找用户
                const user = allUsers.find(u => u.username === username);
                if (user && user.signature) {
                    resolve({ success: true, signature: user.signature });
                } else {
                    resolve({ success: false, signature: null });
                }
            });
        }

        // 添加模拟的API端点
        // 这是为了模拟后端API，在实际应用中应该由后端提供
        window.addEventListener('fetch', function(event) {
            if (event.request.url.includes('/getUserSignature')) {
                event.respondWith(
                    (async function() {
                        const url = new URL(event.request.url);
                        const username = url.searchParams.get('username');
                        const result = await getUserSignature(username);
                        return new Response(JSON.stringify(result), {
                            headers: { 'Content-Type': 'application/json' }
                        });
                    })()
                );
            }
        });

        // 图片优化函数
        function optimizeImage(imgElement) {
            // 如果图片已经优化过，则跳过
            if (imgElement.dataset.optimized) return;

            // 标记图片已优化
            imgElement.dataset.optimized = 'true';

            // 保存原始图片URL
            const originalSrc = imgElement.src;

            // 创建一个新的Image对象
            const img = new Image();
            img.crossOrigin = 'Anonymous';

            img.onload = function() {
                // 创建canvas
                const canvas = document.createElement('canvas');

                // 计算新的尺寸，移动端使用较小的尺寸
                const maxWidth = 120;
                const maxHeight = 60;
                let width = img.width;
                let height = img.height;

                // 保持宽高比例的情况下调整尺寸
                if (width > maxWidth || height > maxHeight) {
                    const ratio = Math.min(maxWidth / width, maxHeight / height);
                    width = width * ratio;
                    height = height * ratio;
                }

                // 设置canvas尺寸
                canvas.width = width;
                canvas.height = height;

                // 绘制图片到canvas
                const ctx = canvas.getContext('2d');
                ctx.drawImage(img, 0, 0, width, height);

                // 将canvas转换为较低质量的JPEG
                try {
                    const optimizedSrc = canvas.toDataURL('image/jpeg', 0.7);
                    imgElement.src = optimizedSrc;
                } catch (e) {
                    console.error('图片优化失败:', e);
                    // 恢复原始图片
                    imgElement.src = originalSrc;
                }
            };

            img.onerror = function() {
                console.error('图片加载失败:', originalSrc);
                // 恢复原始图片
                imgElement.src = originalSrc;
            };

            // 加载图片
            img.src = originalSrc;
        }

        // 更新当前视图
        function updateCurrentView() {
            // 获取当前活动的侧边栏按钮
            const activeBtn = document.querySelector('.side-nav-btn.active-indicator');
            if (!activeBtn) {
                // 如果没有活动的侧边栏按钮，默认更新历史列表
                updateHistoryList();
                return;
            }

            const section = activeBtn.dataset.section;
            if (section === 'history') {
                updateHistoryList();
            } else if (section === 'pendingApproval') {
                updatePendingApprovalList();
            } else if (section === 'approved') {
                updateApprovedList();
            } else {
                // 默认更新历史列表
                updateHistoryList();
            }
        }

        // 刷新数据但保持当前页面
        async function refreshData() {
            try {
                // 显示加载指示器
                const loadingIndicator = document.createElement('div');
                loadingIndicator.id = 'refreshIndicator';
                loadingIndicator.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
                loadingIndicator.textContent = '正在刷新数据，请稍候...';
                document.body.appendChild(loadingIndicator);

                // 直接从DOM中获取当前活动的页面
                const activeNavBtn = document.querySelector('.nav-btn.active');
                const currentSection = activeNavBtn ? activeNavBtn.dataset.section : 'new';

                // 加载最新数据
                await loadApplications(false); // 传入false表示不自动更新视图

                // 根据当前页面手动更新视图
                if (currentSection === 'history') {
                    updateHistoryList();
                } else if (currentSection === 'pendingApproval') {
                    updatePendingApprovalList();
                } else if (currentSection === 'approved') {
                    updateApprovedList();
                } else if (currentSection === 'manageUsers' && currentRole === 'admin') {
                    loadUsers();
                }

                // 移除加载指示器
                setTimeout(() => {
                    const indicator = document.getElementById('refreshIndicator');
                    if (indicator) {
                        indicator.style.opacity = '0';
                        indicator.style.transition = 'opacity 0.5s';
                        setTimeout(() => {
                            indicator.remove();
                        }, 500);
                    }
                }, 500);

                console.log('数据刷新完成');
            } catch (error) {
                console.error('刷新数据失败:', error);
                alert('刷新数据失败，请稍后再试');
            }
        }

        function canUserApprove(app) {
            // 检查当前用户是否是申请的审核人或已经审核过申请
            if (currentRole === 'director') {
                // 厂长需要检查是否是指定的审核人
                const isPendingApprover = app.status === '待厂长审核' &&
                       app.approvals &&
                       app.approvals.directors &&
                       app.approvals.directors[currentUser];

                // 检查是否已经审核过
                const hasApproved = app.approvals &&
                              app.approvals.directors &&
                              app.approvals.directors[currentUser] &&
                              (app.approvals.directors[currentUser].status === 'approved' ||
                               app.approvals.directors[currentUser].status === 'rejected');

                return isPendingApprover || hasApproved;
            } else if (currentRole === 'chief') {
                // 总监需要检查申请状态
                const isPendingApprover = app.status === '待总监审批';

                // 检查是否已经审核过
                const hasApproved = app.approvals &&
                              app.approvals.chief &&
                              (app.approvals.chief.status === 'approved' ||
                               app.approvals.chief.status === 'rejected');

                return isPendingApprover || hasApproved;
            } else if (currentRole === 'manager') {
                // 经理需要检查是否是指定的审核人
                const isPendingApprover = app.status === '待经理审批' &&
                       app.approvals &&
                       app.approvals.managers &&
                       app.approvals.managers[currentUser];

                // 检查是否已经审核过
                const hasApproved = app.approvals &&
                              app.approvals.managers &&
                              app.approvals.managers[currentUser] &&
                              (app.approvals.managers[currentUser].status === 'approved' ||
                               app.approvals.managers[currentUser].status === 'rejected');

                return isPendingApprover || hasApproved;
            } else if (currentRole === 'ceo') {
                // CEO需要检查申请状态
                const isPendingApprover = app.status === '待CEO审批';

                // 检查是否已经作为CEO审核过
                const hasCeoApproved = app.approvals &&
                              app.approvals.ceo &&
                              (app.approvals.ceo.status === 'approved' ||
                               app.approvals.ceo.status === 'rejected');

                // 检查是否之前作为经理审核过
                const hasManagerApproved = app.approvals &&
                              app.approvals.managers &&
                              app.approvals.managers[currentUser] &&
                              (app.approvals.managers[currentUser].status === 'approved' ||
                               app.approvals.managers[currentUser].status === 'rejected');

                return isPendingApprover || hasCeoApproved || hasManagerApproved;
            }
            return false;
        }

        // 渲染已审核列表
        function renderApprovedList(apps, tbody) {
            tbody.innerHTML = apps.map(app => {
                // 获取当前用户的审核结果
                let approvalResult = '未知';

                try {
                    if (currentRole === 'director' && app.approvals && app.approvals.directors && app.approvals.directors[currentUser]) {
                        approvalResult = statusMap[app.approvals.directors[currentUser].status] || app.approvals.directors[currentUser].status;
                    } else if (currentRole === 'chief' && app.approvals && app.approvals.chief) {
                        approvalResult = statusMap[app.approvals.chief.status] || app.approvals.chief.status;
                    } else if (currentRole === 'manager' && app.approvals && app.approvals.managers && app.approvals.managers[currentUser]) {
                        approvalResult = statusMap[app.approvals.managers[currentUser].status] || app.approvals.managers[currentUser].status;
                    } else if (currentRole === 'ceo') {
                        // CEO能看到自己作为CEO的审批结果，以及之前作为经理的审批结果
                        if (app.approvals && app.approvals.ceo) {
                            approvalResult = statusMap[app.approvals.ceo.status] || app.approvals.ceo.status;
                        } else if (app.approvals && app.approvals.managers && app.approvals.managers[currentUser]) {
                            approvalResult = statusMap[app.approvals.managers[currentUser].status] || app.approvals.managers[currentUser].status;
                        }
                    } else if (currentRole === 'admin' || currentRole === 'user' || currentRole === 'readonly') {
                        approvalResult = app.status === '已通过' ? '通过' : '拒绝';
                    }
                } catch (error) {
                    console.error('获取审批结果出错:', error, app);
                }

                return `
                    <tr>
                        <td class="px-6 py-4">${app.applicationCode || '无编号'}</td>
                        <td class="px-6 py-4">${sanitizeInput(app.applicant)}</td>
                        <td class="px-6 py-4">${sanitizeInput(app.department)}</td>
                        <td class="px-6 py-4">
                            ${app.date}
                            <span class="block text-xs text-gray-500">${formatSubmitTime(app.id)}</span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 rounded ${getPriorityClass(app.priority)}">${priorityMap[app.priority] || app.priority}</span>
                        </td>
                        <td class="px-6 py-4" style="max-width: 200px; width: 200px; white-space: normal; overflow: visible; word-break: break-word; vertical-align: top;">
                            ${createClickableContent(app.content)}
                        </td>
                        <td class="px-6 py-4">
                            ${app.status}
                            <span class="block text-xs text-gray-500">${getApplicationStatusDesc(app.status, app)}</span>
                            ${app.status === '待厂长审核' && app.approvals && app.approvals.directors ?
                                `<span class="block text-xs text-blue-600">待审核厂长：${Object.entries(app.approvals.directors)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([director, _]) => sanitizeInput(director))
                                    .join('、') || '无'}</span>`
                                : ''}
                            ${app.status === '待经理审批' && app.approvals && app.approvals.managers ?
                                `<span class="block text-xs text-blue-600">待审核经理：${Object.entries(app.approvals.managers)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([manager, _]) => sanitizeInput(manager))
                                    .join('、') || '无'}</span>`
                                : ''}
                        </td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 rounded ${approvalResult === '通过' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'}">${approvalResult}</span>
                        </td>
                        <td class="px-6 py-4">
                            <button onclick="viewDetail(${app.id})" class="text-blue-600 hover:text-blue-800">查看</button>
                            ${canWithdrawApproval(app) ?
                                `<button onclick="handleWithdrawApproval(${app.id})" class="text-orange-600 hover:text-orange-800 ml-2">撤回</button>` :
                                ''}
                        </td>
                    </tr>
                `;
            }).join('');
        }

        // 渲染待审核列表
        function renderPendingList(apps, tbody) {
            tbody.innerHTML = apps.map(app => {
                // 获取当前用户的审核状态
                let approvalStatus = '未审核';
                let approvalClass = 'bg-gray-100 text-gray-800';

                try {
                    if (currentRole === 'director' && app.approvals && app.approvals.directors && app.approvals.directors[currentUser]) {
                        approvalStatus = statusMap[app.approvals.directors[currentUser].status] || app.approvals.directors[currentUser].status;
                        approvalClass = app.approvals.directors[currentUser].status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    } else if (currentRole === 'chief' && app.approvals && app.approvals.chief) {
                        approvalStatus = statusMap[app.approvals.chief.status] || app.approvals.chief.status;
                        approvalClass = app.approvals.chief.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    } else if (currentRole === 'manager' && app.approvals && app.approvals.managers && app.approvals.managers[currentUser]) {
                        approvalStatus = statusMap[app.approvals.managers[currentUser].status] || app.approvals.managers[currentUser].status;
                        approvalClass = app.approvals.managers[currentUser].status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    } else if (currentRole === 'ceo' && app.approvals && app.approvals.ceo) {
                        approvalStatus = statusMap[app.approvals.ceo.status] || app.approvals.ceo.status;
                        approvalClass = app.approvals.ceo.status === 'approved' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800';
                    }
                } catch (error) {
                    console.error('获取审批状态出错:', error, app);
                }

                return `
                    <tr>
                        <td class="px-6 py-4">${sanitizeInput(app.applicant)}</td>
                        <td class="px-6 py-4">${sanitizeInput(app.department)}</td>
                        <td class="px-6 py-4">
                            ${app.date}
                            <span class="block text-xs text-gray-500">${formatSubmitTime(app.id)}</span>
                        </td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 rounded ${getPriorityClass(app.priority)}">${priorityMap[app.priority] || app.priority}</span>
                        </td>
                        <td class="px-6 py-4" style="max-width: 200px; width: 200px; white-space: normal; overflow: visible; word-break: break-word; vertical-align: top;">
                            ${createClickableContent(app.content)}
                        </td>
                        <td class="px-6 py-4">
                            ${app.status}
                            <span class="block text-xs text-gray-500">${getApplicationStatusDesc(app.status, app)}</span>
                            ${app.status === '待厂长审核' && app.approvals && app.approvals.directors ?
                                `<span class="block text-xs text-blue-600">待审核厂长：${Object.entries(app.approvals.directors)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([director, _]) => sanitizeInput(director))
                                    .join('、') || '无'}</span>`
                                : ''}
                            ${app.status === '待经理审批' && app.approvals && app.approvals.managers ?
                                `<span class="block text-xs text-blue-600">待审核经理：${Object.entries(app.approvals.managers)
                                    .filter(([_, approval]) => approval.status === 'pending')
                                    .map(([manager, _]) => sanitizeInput(manager))
                                    .join('、') || '无'}</span>`
                                : ''}
                        </td>
                        <td class="px-6 py-4">
                            <span class="px-2 py-1 rounded ${approvalClass}">${approvalStatus}</span>
                        </td>
                        <td class="px-6 py-4">
                            <button onclick="viewDetail(${app.id})" class="text-blue-600 hover:text-blue-800">查看</button>
                        </td>
                    </tr>
                `;
            }).join('');
        }



        // 更新用户信息显示
        function updateUserInfo() {
            const userInfoElement = document.getElementById('currentUserInfo');
            const mobileUserInfoElement = document.getElementById('mobileCurrentUserInfo');
            const pcUserInfoElement = document.getElementById('pcCurrentUserInfo');

            const userDisplayText = currentUser;

            if (userInfoElement) {
                userInfoElement.textContent = `当前用户: ${currentUser}`;
            }

            if (mobileUserInfoElement) {
                mobileUserInfoElement.textContent = `当前用户: ${currentUser}`;
            }

            if (pcUserInfoElement) {
                pcUserInfoElement.textContent = userDisplayText;
            }
        }

        // 切换移动端用户信息下拉菜单
        function toggleUserInfo() {
            const dropdown = document.getElementById('userInfoDropdown');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
            }
        }



        // 点击页面其他地方关闭用户信息下拉菜单
        document.addEventListener('click', function(event) {
            const dropdown = document.getElementById('userInfoDropdown');
            const userButton = document.querySelector('.md\\:hidden button');

            if (dropdown && !dropdown.classList.contains('hidden') &&
                !dropdown.contains(event.target) &&
                userButton && !userButton.contains(event.target)) {
                dropdown.classList.add('hidden');
            }
        });

        // 导出用户数据为CSV
        async function exportUsers() {
            try {
                // 显示加载指示器
                showLoadingIndicator('正在导出用户数据...');

                // 使用fetch发起请求，但设置responseType为blob以接收二进制数据
                const response = await fetch(`/exportUsers?username=${currentUser}`);

                // 隐藏加载指示器
                hideLoadingIndicator();

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '导出失败');
                }

                // 获取blob数据
                const blob = await response.blob();

                // 创建下载链接
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = 'users.csv';

                // 添加到文档并触发点击
                document.body.appendChild(a);
                a.click();

                // 清理
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            } catch (error) {
                hideLoadingIndicator();
                console.error('导出用户数据失败:', error);
                alert('导出用户数据失败: ' + error.message);
            }
        }

        // 时间控制相关函数
        function toggleWorkingDays() {
            const checkbox = document.getElementById('enableWorkingDays');
            const settings = document.getElementById('workingDaysSettings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
            } else {
                settings.classList.add('hidden');
            }
        }

        function toggleCustomDates() {
            const checkbox = document.getElementById('enableCustomDates');
            const settings = document.getElementById('customDatesSettings');
            if (checkbox.checked) {
                settings.classList.remove('hidden');
            } else {
                settings.classList.add('hidden');
            }
        }

        function addSkipDate() {
            const dateInput = document.getElementById('newSkipDate');
            const date = dateInput.value;
            if (!date) {
                alert('请选择一个日期');
                return;
            }

            // 检查是否已存在
            const existingDates = Array.from(document.querySelectorAll('#skipDatesList .skip-date-item')).map(item => item.dataset.date);
            if (existingDates.includes(date)) {
                alert('该日期已存在');
                return;
            }

            // 添加到列表
            const listContainer = document.getElementById('skipDatesList');

            // 如果列表为空，先清除提示文字
            if (listContainer.innerHTML.includes('暂无跳过日期')) {
                listContainer.innerHTML = '';
            }

            const dateItem = document.createElement('div');
            dateItem.className = 'skip-date-item flex items-center justify-between bg-white p-2 rounded border text-sm';
            dateItem.dataset.date = date;
            dateItem.innerHTML = `
                <span>${date}</span>
                <button onclick="removeSkipDate('${date}')" class="text-red-600 hover:text-red-800 text-xs">删除</button>
            `;
            listContainer.appendChild(dateItem);

            // 清空输入框
            dateInput.value = '';
        }

        function addDateRange() {
            const startDateInput = document.getElementById('rangeStartDate');
            const endDateInput = document.getElementById('rangeEndDate');
            const startDate = startDateInput.value;
            const endDate = endDateInput.value;

            if (!startDate || !endDate) {
                alert('请选择开始日期和结束日期');
                return;
            }

            if (startDate > endDate) {
                alert('开始日期不能晚于结束日期');
                return;
            }

            // 生成日期范围
            const start = new Date(startDate);
            const end = new Date(endDate);
            const dates = [];

            for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
                const dateStr = d.toISOString().slice(0, 10);
                dates.push(dateStr);
            }

            // 检查重复并添加
            const existingDates = Array.from(document.querySelectorAll('#skipDatesList .skip-date-item')).map(item => item.dataset.date);
            const newDates = dates.filter(date => !existingDates.includes(date));

            if (newDates.length === 0) {
                alert('选择的日期范围内的所有日期都已存在');
                return;
            }

            // 添加新日期到列表
            const listContainer = document.getElementById('skipDatesList');
            newDates.forEach(date => {
                const dateItem = document.createElement('div');
                dateItem.className = 'skip-date-item flex items-center justify-between bg-white p-2 rounded border text-sm';
                dateItem.dataset.date = date;
                dateItem.innerHTML = `
                    <span>${date}</span>
                    <button onclick="removeSkipDate('${date}')" class="text-red-600 hover:text-red-800 text-xs">删除</button>
                `;
                listContainer.appendChild(dateItem);
            });

            // 清空输入框
            startDateInput.value = '';
            endDateInput.value = '';

            alert(`成功添加 ${newDates.length} 个日期`);
        }

        function removeSkipDate(date) {
            const item = document.querySelector(`#skipDatesList .skip-date-item[data-date="${date}"]`);
            if (item) {
                item.remove();
            }
        }

        // 系统设置相关函数
        async function loadSystemSettings() {
            try {
                const response = await fetch(`/getReminderSettings?username=${currentUser}`);
                const result = await response.json();

                if (result.success) {
                    // 加载优先级策略
                    const priority = result.settings.priority;
                    document.getElementById('highPriorityDelay').value = priority.high.initialDelay;
                    document.getElementById('highPriorityNormal').value = priority.high.normalInterval;
                    document.getElementById('highPriorityMedium').value = priority.high.mediumInterval;
                    document.getElementById('highPriorityUrgent').value = priority.high.urgentInterval;

                    document.getElementById('mediumPriorityDelay').value = priority.medium.initialDelay;
                    document.getElementById('mediumPriorityNormal').value = priority.medium.normalInterval;
                    document.getElementById('mediumPriorityMedium').value = priority.medium.mediumInterval;
                    document.getElementById('mediumPriorityUrgent').value = priority.medium.urgentInterval;

                    document.getElementById('lowPriorityDelay').value = priority.low.initialDelay;
                    document.getElementById('lowPriorityNormal').value = priority.low.normalInterval;
                    document.getElementById('lowPriorityMedium').value = priority.low.mediumInterval;
                    document.getElementById('lowPriorityUrgent').value = priority.low.urgentInterval;

                    // 加载时间控制设置
                    const timeControl = result.settings.timeControl || {};

                    // 工作日和工作时间设置
                    const workingDays = timeControl.workingDays || {};

                    const enableWorkingDaysCheckbox = document.getElementById('enableWorkingDays');
                    if (enableWorkingDaysCheckbox) {
                        enableWorkingDaysCheckbox.checked = workingDays.enabled || false;
                    }

                    document.getElementById('workingStartTime').value = workingDays.startTime || '09:00';
                    document.getElementById('workingEndTime').value = workingDays.endTime || '18:00';

                    // 设置工作日复选框
                    const selectedDays = workingDays.days || [1, 2, 3, 4, 5]; // 默认周一到周五
                    for (let i = 1; i <= 7; i++) {
                        const checkbox = document.getElementById(`workDay${i}`);
                        if (checkbox) {
                            checkbox.checked = selectedDays.includes(i);
                        }
                    }

                    // 自定义日期设置
                    const customDates = timeControl.customDates || {};
                    document.getElementById('enableCustomDates').checked = customDates.enabled || false;

                    // 加载跳过日期列表
                    const skipDates = customDates.skipDates || [];
                    const listContainer = document.getElementById('skipDatesList');
                    listContainer.innerHTML = '';
                    if (skipDates.length === 0) {
                        listContainer.innerHTML = '<p class="text-gray-500 text-xs text-center py-2">暂无跳过日期</p>';
                    } else {
                        // 按日期排序
                        skipDates.sort().forEach(date => {
                            const dateItem = document.createElement('div');
                            dateItem.className = 'skip-date-item flex items-center justify-between bg-white p-2 rounded border text-sm';
                            dateItem.dataset.date = date;
                            dateItem.innerHTML = `
                                <span>${date}</span>
                                <button onclick="removeSkipDate('${date}')" class="text-red-600 hover:text-red-800 text-xs">删除</button>
                            `;
                            listContainer.appendChild(dateItem);
                        });
                    }

                    // 移除旧的事件监听器（如果存在）
                    const enableWorkingDaysEl = document.getElementById('enableWorkingDays');
                    const enableCustomDatesEl = document.getElementById('enableCustomDates');

                    if (enableWorkingDaysEl) {
                        enableWorkingDaysEl.removeEventListener('change', toggleWorkingDays);
                        enableWorkingDaysEl.addEventListener('change', toggleWorkingDays);
                    }

                    if (enableCustomDatesEl) {
                        enableCustomDatesEl.removeEventListener('change', toggleCustomDates);
                        enableCustomDatesEl.addEventListener('change', toggleCustomDates);
                    }

                    // 初始化显示状态
                    toggleWorkingDays();
                    toggleCustomDates();
                }

                // 加载统计信息
                loadReminderStats();
            } catch (error) {
                console.error('加载系统设置失败:', error);
                alert('加载系统设置失败');
            }
        }



        async function saveReminderStrategies() {
            try {
                const strategies = {
                    priority: {
                        high: {
                            initialDelay: parseInt(document.getElementById('highPriorityDelay').value),
                            normalInterval: parseInt(document.getElementById('highPriorityNormal').value),
                            mediumInterval: parseInt(document.getElementById('highPriorityMedium').value),
                            urgentInterval: parseInt(document.getElementById('highPriorityUrgent').value)
                        },
                        medium: {
                            initialDelay: parseInt(document.getElementById('mediumPriorityDelay').value),
                            normalInterval: parseInt(document.getElementById('mediumPriorityNormal').value),
                            mediumInterval: parseInt(document.getElementById('mediumPriorityMedium').value),
                            urgentInterval: parseInt(document.getElementById('mediumPriorityUrgent').value)
                        },
                        low: {
                            initialDelay: parseInt(document.getElementById('lowPriorityDelay').value),
                            normalInterval: parseInt(document.getElementById('lowPriorityNormal').value),
                            mediumInterval: parseInt(document.getElementById('lowPriorityMedium').value),
                            urgentInterval: parseInt(document.getElementById('lowPriorityUrgent').value)
                        }
                    },
                    timeControl: {
                        workingDays: {
                            enabled: document.getElementById('enableWorkingDays').checked,
                            days: Array.from(document.querySelectorAll('[id^="workDay"]:checked')).map(cb => parseInt(cb.value)),
                            startTime: document.getElementById('workingStartTime').value,
                            endTime: document.getElementById('workingEndTime').value
                        },
                        customDates: {
                            enabled: document.getElementById('enableCustomDates').checked,
                            skipDates: Array.from(document.querySelectorAll('#skipDatesList .skip-date-item')).map(item => item.dataset.date)
                        }
                    }
                };

                const response = await fetch('/saveReminderStrategies', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: currentUser,
                        strategies
                    })
                });

                const result = await response.json();
                if (result.success) {
                    alert('提醒策略保存成功');
                } else {
                    alert('保存失败：' + result.message);
                }
            } catch (error) {
                console.error('保存提醒策略失败:', error);
                alert('保存提醒策略失败');
            }
        }

        // 手动触发提醒检查
        async function triggerReminderCheck() {
            try {
                // 显示加载指示器
                showLoadingIndicator('正在触发提醒检查...');

                const response = await fetch('/triggerReminderCheck', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username: currentUser
                    })
                });

                // 隐藏加载指示器
                hideLoadingIndicator();

                const result = await response.json();
                if (result.success) {
                    alert(result.message);
                    // 刷新统计信息
                    await loadReminderStats();
                } else {
                    alert('触发失败：' + result.message);
                }
            } catch (error) {
                hideLoadingIndicator();
                console.error('触发提醒检查失败:', error);
                alert('触发提醒检查失败');
            }
        }

        async function loadReminderStats() {
            try {
                const response = await fetch(`/getReminderStats?username=${currentUser}`);
                const result = await response.json();

                if (result.success) {
                    document.getElementById('totalReminders').textContent = result.stats.totalReminders;
                    document.getElementById('pendingApplications').textContent = result.stats.pendingApplications;
                    document.getElementById('urgentApplications').textContent = result.stats.urgentApplications;
                    document.getElementById('avgResponseTime').textContent = result.stats.avgResponseTime;
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
            }
        }

        // 导出申请记录为Excel
        async function exportApplicationsToExcel() {
            try {
                // 显示加载指示器
                showLoadingIndicator('正在导出申请记录...');

                // 获取当前时间范围
                const timeRange = document.getElementById('timeRange').value;

                // 构建请求URL
                let url = `/exportApplications?username=${currentUser}&role=${currentRole}&timeRange=${timeRange}`;

                // 检查是否启用了日期范围筛选
                const dateRangeContainer = document.getElementById('exportDateRangeContainer');
                if (!dateRangeContainer.classList.contains('hidden')) {
                    // 获取日期范围
                    const startDate = document.getElementById('exportStartDate').value;
                    const endDate = document.getElementById('exportEndDate').value;

                    // 验证日期
                    if (!startDate || !endDate) {
                        hideLoadingIndicator();
                        alert('请选择完整的日期范围');
                        return;
                    }

                    // 验证开始日期不能大于结束日期
                    if (new Date(startDate) > new Date(endDate)) {
                        hideLoadingIndicator();
                        alert('开始日期不能大于结束日期');
                        return;
                    }

                    // 添加日期范围参数
                    url += `&startDate=${startDate}&endDate=${endDate}`;
                }

                // 使用fetch发起请求
                const response = await fetch(url);

                // 隐藏加载指示器
                hideLoadingIndicator();

                if (!response.ok) {
                    const errorData = await response.json();
                    throw new Error(errorData.message || '导出失败');
                }

                // 获取blob数据
                const blob = await response.blob();

                // 创建下载链接
                const downloadUrl = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = downloadUrl;

                // 设置文件名
                let fileName = '';

                // 检查是否使用了日期范围筛选
                if (!dateRangeContainer.classList.contains('hidden')) {
                    const startDate = document.getElementById('exportStartDate').value;
                    const endDate = document.getElementById('exportEndDate').value;
                    fileName = `申请记录_${startDate}_至_${endDate}.xlsx`;
                } else {
                    // 根据时间范围设置文件名
                    let timeRangeText = '';
                    switch(timeRange) {
                        case 'week':
                            timeRangeText = '本周';
                            break;
                        case 'month':
                            timeRangeText = '本月';
                            break;
                        case 'year':
                            timeRangeText = '本年';
                            break;
                        default:
                            timeRangeText = '全部';
                    }

                    // 设置文件名，包含当前日期
                    const today = new Date().toISOString().slice(0, 10);
                    fileName = `申请记录_${timeRangeText}_${today}.xlsx`;
                }

                a.download = fileName;

                // 添加到文档并触发点击
                document.body.appendChild(a);
                a.click();

                // 清理
                window.URL.revokeObjectURL(downloadUrl);
                document.body.removeChild(a);

                console.log('申请记录导出成功');
            } catch (error) {
                hideLoadingIndicator();
                console.error('导出申请记录失败:', error);
                alert('导出申请记录失败: ' + error.message);
            }
        }

        // 导入用户数据
        async function importUsers(event) {
            const file = event.target.files[0];
            if (!file) return;

            // 确认导入
            if (!confirm('导入将添加新用户，并更新已存在的用户信息（密码、角色、邮箱等）。\n\n注意：电子签名不会通过导入更新，需要单独设置。\n\n确定要继续吗？')) {
                event.target.value = ''; // 清空文件选择
                return;
            }

            try {
                // 显示加载指示器
                showLoadingIndicator('正在导入用户数据...');

                // 读取文件内容
                const fileContent = await new Promise((resolve, reject) => {
                    const reader = new FileReader();
                    reader.onload = e => resolve(e.target.result);
                    reader.onerror = e => reject(e);
                    reader.readAsText(file, 'UTF-8'); // 指定UTF-8编码
                });

                // 发送到服务器
                const response = await fetch(`/importUsers?username=${currentUser}`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'text/csv; charset=utf-8' },
                    body: fileContent
                });

                // 隐藏加载指示器
                hideLoadingIndicator();

                const result = await response.json();

                if (result.success) {
                    alert(result.message);
                    await loadUsers(); // 重新加载用户列表
                    renderUsersList();
                } else {
                    let errorMessage = result.message;
                    if (result.errors && result.errors.length > 0) {
                        errorMessage += '\n\n错误详情:\n' + result.errors.join('\n');
                    }
                    alert(errorMessage);
                }
            } catch (error) {
                hideLoadingIndicator();
                console.error('导入用户数据失败:', error);
                alert('导入用户数据失败: ' + error.message);
            } finally {
                event.target.value = ''; // 清空文件选择，允许再次选择同一文件
            }
        }

        // 显示加载指示器
        function showLoadingIndicator(message) {
            const loadingIndicator = document.createElement('div');
            loadingIndicator.id = 'globalLoadingIndicator';
            loadingIndicator.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
            loadingIndicator.innerHTML = `
                <div class="bg-white p-4 rounded-md shadow-lg">
                    <div class="spinner mb-2"></div>
                    <p>${message || '加载中...'}</p>
                </div>
            `;
            document.body.appendChild(loadingIndicator);
        }

        // 隐藏加载指示器
        function hideLoadingIndicator() {
            const loadingIndicator = document.getElementById('globalLoadingIndicator');
            if (loadingIndicator) {
                document.body.removeChild(loadingIndicator);
            }
        }

        // 显示修改密码弹窗
        function showChangePasswordModal() {
            console.log('显示修改密码弹窗');
            // 重置表单
            document.getElementById('changePasswordForm').reset();
            document.getElementById('cpError').textContent = '';
            document.getElementById('cpError').classList.add('hidden');
            document.getElementById('cpSuccess').textContent = '';
            document.getElementById('cpSuccess').classList.add('hidden');

            // 显示弹窗
            document.getElementById('changePasswordModal').classList.remove('hidden');
        }

        // 隐藏修改密码弹窗
        function hideChangePasswordModal() {
            console.log('隐藏修改密码弹窗');
            document.getElementById('changePasswordModal').classList.add('hidden');
        }

        // 处理修改密码
        async function handleChangePassword(e) {
            e.preventDefault();
            console.log('处理修改密码请求');

            // 获取表单数据
            const username = document.getElementById('cpUsername').value.trim();
            const currentPassword = document.getElementById('cpCurrentPassword').value;
            const confirmCurrentPassword = document.getElementById('cpConfirmCurrentPassword').value;
            const newPassword = document.getElementById('cpNewPassword').value;

            // 验证表单
            if (!username || !currentPassword || !confirmCurrentPassword || !newPassword) {
                showChangePasswordError('请填写所有字段');
                return;
            }

            // 验证两次输入的当前密码是否一致
            if (currentPassword !== confirmCurrentPassword) {
                showChangePasswordError('两次输入的当前密码不一致');
                return;
            }

            // 验证新密码不能与当前密码相同
            if (newPassword === currentPassword) {
                showChangePasswordError('新密码不能与当前密码相同');
                return;
            }

            try {
                // 显示loading
                const cpSubmitBtn = document.querySelector('#changePasswordForm button[type="submit"]');
                const originalBtnText = cpSubmitBtn.textContent;
                cpSubmitBtn.textContent = '处理中...';
                cpSubmitBtn.disabled = true;

                console.log('发送修改密码请求');
                // 发送修改密码请求
                const response = await fetch('/changePassword', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({
                        username,
                        currentPassword,
                        newPassword
                    })
                });

                const result = await response.json();
                console.log('修改密码响应:', result);

                // 恢复按钮状态
                cpSubmitBtn.textContent = originalBtnText;
                cpSubmitBtn.disabled = false;

                if (result.success) {
                    // 显示成功消息
                    document.getElementById('cpSuccess').textContent = result.message;
                    document.getElementById('cpSuccess').classList.remove('hidden');
                    document.getElementById('cpError').classList.add('hidden');

                    // 清空表单
                    document.getElementById('changePasswordForm').reset();

                    // 如果用户已登录且修改的是当前用户的密码，则注销
                    if (sessionStorage.getItem('isLoggedIn') === 'true' && sessionStorage.getItem('username') === username) {
                        // 延迟3秒后自动登出
                        setTimeout(() => {
                            logout();
                            hideChangePasswordModal();
                            alert('密码已修改，请使用新密码重新登录');
                        }, 3000);
                    }
                } else {
                    // 显示错误消息
                    showChangePasswordError(result.message);
                }
            } catch (error) {
                console.error('修改密码失败:', error);
                showChangePasswordError('修改密码失败，请稍后再试');

                // 恢复按钮状态
                const cpSubmitBtn = document.querySelector('#changePasswordForm button[type="submit"]');
                cpSubmitBtn.textContent = '确认修改';
                cpSubmitBtn.disabled = false;
            }
        }

        // 显示修改密码错误
        function showChangePasswordError(message) {
            console.log('显示错误消息:', message);
            const errorElement = document.getElementById('cpError');
            errorElement.textContent = message;
            errorElement.classList.remove('hidden');
            document.getElementById('cpSuccess').classList.add('hidden');
        }

        // 检查用户是否可以撤回审批
        function canWithdrawApproval(app) {
            // 只有总监可以撤回审批，且仅限于已通过的申请（总监直接通过且未经过经理和CEO审批的），只读角色不能撤回审批
            return (
                currentRole === 'chief' &&
                app.status === '已通过' &&
                app.approvals.chief &&
                app.approvals.chief.status === 'approved' &&
                (!app.approvals.managers || Object.keys(app.approvals.managers).length === 0) &&
                (!app.approvals.ceo || !app.approvals.ceo.status || app.approvals.ceo.status === 'pending')
            );
        }

        // 处理撤回审批操作
        async function handleWithdrawApproval(id) {
            if (!confirm('确认撤回审批？撤回后将重新进入总监审批环节。')) {
                return;
            }

            // 显示处理中提示
            const msgElement = document.createElement('div');
            msgElement.id = 'processingMessage';
            msgElement.className = 'fixed top-0 left-0 w-full bg-blue-500 text-white text-center py-2 z-50';
            msgElement.textContent = '处理中，请稍候...';
            document.body.appendChild(msgElement);

            try {
                const response = await fetch('/withdrawApproval', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        id: id,
                        username: currentUser,
                        role: currentRole
                    }),
                });

                const result = await response.json();

                // 移除处理中提示
                document.body.removeChild(msgElement);

                if (result.success) {
                    alert('审批已成功撤回，申请将重新进入总监审批环节');

                    // 更新本地应用数据
                    const appIndex = applications.findIndex(a => a.id === id);
                    if (appIndex !== -1) {
                        applications[appIndex] = result.application;
                    }

                    // 重新生成详情内容
                    generateDetailContent(result.application, false);

                    // 刷新应用列表
                    loadApplications();
                } else {
                    alert('撤回失败：' + (result.message || '未知错误'));
                }
            } catch (error) {
                console.error('撤回审批时出错:', error);
                // 移除处理中提示
                document.body.removeChild(msgElement);
                alert('操作失败，请稍后重试');
            }
        }
    </script>

    <style>
        /* 申请内容列宽度控制 */
        table.min-w-full th:nth-child(6),
        table.min-w-full td:nth-child(6) {
            max-width: 200px !important;
            width: 200px !important;
            white-space: normal !important;
            overflow: visible !important;
            word-break: break-word !important;
            table-layout: fixed !important;
            vertical-align: top !important;
            padding-top: 12px !important;
            padding-bottom: 12px !important;
        }

        /* 确保表格使用固定布局 */
        table.min-w-full {
            table-layout: fixed !important;
        }

        .nav-btn {
            padding: 8px 16px;
            border-radius: 4px;
            transition: background-color 0.2s;
        }
        .nav-btn.active {
            background-color: rgba(255,255,255,0.15);
            font-weight: 600;
        }
        .side-nav-btn {
            transition: all 0.2s;
        }
        .side-nav-btn.active-indicator {
            background-color: rgba(255,255,255,0.25);
            font-weight: 600;
            box-shadow: 0 0 10px rgba(255,255,255,0.1);
        }
        #fileInputArea:hover, #editFileInputArea:hover {
            background-color: rgba(59, 130, 246, 0.05);
        }
        #detailModal, #editModal, #previewModal, #sideMenu {
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
        }

        /* 自定义滚动条样式 */
        #sideMenu::-webkit-scrollbar {
            width: 6px;
        }

        #sideMenu::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
        }

        #sideMenu::-webkit-scrollbar-thumb {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 3px;
        }

        #sideMenu::-webkit-scrollbar-thumb:hover {
            background: rgba(255, 255, 255, 0.3);
        }

        /* PC端侧边栏样式 */
        #pcSidebar {
            height: 100vh;
            position: fixed;
            top: 0;
            left: 0;
            z-index: 30;
            background: white;
            border-right: 1px solid #e5e7eb;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }

        /* PC端布局调整 - 固定侧边栏 */
        @media (min-width: 768px) {
            #pcSidebar {
                position: fixed !important;
                left: 1rem !important;
                top: 1rem !important;
                bottom: 1rem !important;
                width: 16rem !important;
                height: auto !important;
                z-index: 30 !important;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                overflow: hidden !important; /* 完全禁止滚动 */
            }

            /* 确保侧边栏内部元素不会导致滚动 */
            #pcSidebar * {
                overflow: visible;
            }

            #pcSidebar .flex-1 {
                overflow: hidden !important;
            }
        }



        /* PC端侧边栏导航按钮样式 */
        .pc-nav-btn {
            transition: all 0.2s ease;
            font-weight: 500;
            border: none;
            background: transparent;
            text-align: left;
        }

        .pc-nav-btn:hover {
            background-color: #f3f4f6 !important;
        }

        .pc-nav-btn.active-indicator {
            background-color: #dbeafe !important;
            color: #1d4ed8 !important;
            font-weight: 600;
            border-left: 3px solid #2563eb;
        }

        .pc-nav-btn.active-indicator svg {
            color: #2563eb !important;
        }

        /* PC端主内容区域调整 - 为固定侧边栏留出空间 */
        @media (min-width: 768px) {
            .flex-1.md\\:overflow-y-auto {
                margin-left: 18rem !important; /* 256px + 16px gap */
                margin-right: 1rem !important;
                margin-top: 1rem !important;
                margin-bottom: 1rem !important;
                height: calc(100vh - 2rem) !important;
                overflow-y: auto !important; /* 确保内容区域可以滚动 */
                overflow-x: hidden; /* 防止水平滚动 */
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
                position: relative;
                z-index: 10;
            }
        }

        /* 移动端保持原有布局 */
        @media (max-width: 767px) {
            .flex-1.md\\:overflow-y-auto {
                margin-left: 0;
                width: 100%;
                background: #f9fafb !important;
                box-shadow: none !important;
                border-radius: 0 !important;
            }

            #pcSidebar {
                position: fixed !important;
                top: 0 !important;
                left: 0 !important;
                bottom: auto !important;
                height: 100vh !important;
                border-radius: 0 !important;
                width: 16rem !important;
            }
        }



        /* 侧边栏样式 - 仅在移动端显示 */
        #sideMenu {
            top: 0;
            height: 100%;
            z-index: 40;
        }

        /* 顶部导航样式 */
        nav.bg-blue-600 {
            z-index: 40;
            position: relative;
        }

        /* PC端顶部导航按钮样式 */
        @media (min-width: 768px) {
            .nav-btn {
                margin: 0 2px;
            }
            .nav-btn:hover {
                background-color: rgba(255,255,255,0.1);
            }
        }

        /* 移动端导航按钮样式 */
        @media (max-width: 767px) {
            .nav-btn {
                margin-bottom: 4px;
            }
        }

        /* 申请书模板样式 */
        .application-template {
            width: 100%;
            border-collapse: collapse;
            border: 1px solid #000;
            font-family: SimSun, serif;
            height: 100%;
        }

        .application-template.mobile {
            font-size: 14px;
        }

        .application-template.mobile-small {
            font-size: 12px;
        }

        .application-template th, .application-template td {
            border: 1px solid #000;
            padding: 8px;
            text-align: center;
            vertical-align: middle;
        }

        .application-template.mobile th, .application-template.mobile td {
            padding: 6px;
        }

        .application-template.mobile-small th, .application-template.mobile-small td {
            padding: 4px;
        }

        .application-template .title {
            text-align: center;
            font-size: 24px;
            font-weight: bold;
            padding: 15px;
            height: 10%;
        }

        .application-template.mobile .title {
            font-size: 20px;
            padding: 10px;
        }

        .application-template.mobile-small .title {
            font-size: 18px;
            padding: 8px;
        }

        .application-template .label {
            width: 15%;
            font-weight: bold;
            text-align: center;
            vertical-align: middle;
        }

        .application-template.mobile .label {
            width: 20%;
        }

        .application-template.mobile-small .label {
            width: 25%;
        }

        .application-template .content {
            height: 200px;
            vertical-align: top;
            text-align: left;
            padding: 10px;
        }

        .application-template.mobile .content {
            height: 40%;
            min-height: 180px;
            padding: 8px;
        }

        .application-template.mobile-small .content {
            height: 40%;
            min-height: 160px;
            padding: 6px;
        }

        .application-template .applicant {
            text-align: right;
            padding-right: 50px;
            height: 5%;
        }

        .application-template.mobile .applicant {
            padding-right: 30px;
        }

        .application-template.mobile-small .applicant {
            padding-right: 20px;
        }

        .application-template .approval {
            min-height: 100px;
            vertical-align: top;
            height: 15%;
        }

        .application-template.mobile .approval {
            min-height: 90px;
        }

        .application-template.mobile-small .approval {
            min-height: 80px;
        }

        /* A4纸张样式 */
        .a4-page {
            width: 210mm;
            height: 297mm;
            margin: 0 auto;
            background: white;
            box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
            position: relative;
            padding: 20mm 20mm 10mm 20mm; /* 减小底部内边距从20mm到10mm */
            box-sizing: border-box;
        }

        /* 审批容器样式 */
        .approval-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
        }

        .single-approval {
            flex: 1;
            min-width: 150px;
            display: flex;
            flex-direction: column;
        }

        .approval-comment {
            margin-bottom: 5px;
            font-size: 14px;
        }

        .signature-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .signature-image {
            max-width: 120px;
            max-height: 60px;
            margin-bottom: 5px;
            image-rendering: -webkit-optimize-contrast; /* 提高图片清晰度 */
            image-rendering: crisp-edges;
            object-fit: contain;
        }

        .approval-date {
            font-size: 12px;
            color: #333; /* 加深颜色提高对比度 */
            text-align: center;
            font-weight: 500; /* 稍微加粗 */
        }

        .application-template.mobile .approval-date {
            font-size: 11px;
        }

        .application-template.mobile-small .approval-date {
            font-size: 10px;
        }

        .no-signature {
            color: #666; /* 加深颜色提高对比度 */
            font-style: italic;
            font-size: 12px;
            margin-bottom: 5px;
            font-weight: 500; /* 稍微加粗 */
        }

        .application-template.mobile .no-signature {
            font-size: 11px;
        }

        .application-template.mobile-small .no-signature {
            font-size: 10px;
        }

        /* 打印样式 */
        @media print {
            @page {
                size: A4;
                margin: 0;
            }

            body * {
                visibility: hidden;
            }

            #applicationTemplateModal, #applicationTemplateModal * {
                visibility: visible;
            }

            #applicationTemplateModal {
                position: absolute;
                left: 0;
                top: 0;
                width: 100%;
                height: 100%;
                background: white;
                display: flex;
                justify-content: center;
                align-items: flex-start;
                overflow: visible;
            }

            .a4-page {
                box-shadow: none;
                margin: 0;
                padding: 20mm;
                width: 210mm;
                height: 297mm;
                overflow: hidden;
                page-break-after: always;
            }

            .application-template {
                page-break-inside: avoid;
                width: 100%;
                height: 100%;
                border-collapse: collapse;
            }

            .print-hint, .template-actions {
                display: none !important;
            }

            /* 确保签名图片在打印时可见 */
            .signature-image {
                max-width: 120px;
                max-height: 60px;
                display: block !important;
                visibility: visible !important;
            }
        }

        /* 响应式样式 */
        @media (max-width: 768px) {
            #detailModal .relative, #editModal .relative, #previewModal .relative {
                margin: 1rem;
                width: calc(100% - 2rem);
            }

            #detailContent, #editForm {
                max-height: 70vh;
            }

            /* 移动端导航栏适配 */
            nav .container {
                flex-direction: column;
                align-items: stretch;
            }

            nav .flex.items-center.space-x-4 {
                flex-direction: column;
                align-items: stretch;
                margin-top: 0.5rem;
                gap: 0.5rem;
            }

            nav .flex.items-center.space-x-4 > * {
                margin: 0.25rem 0;
            }

            /* 移动端表格适配 */
            table {
                display: block;
                overflow-x: auto;
                white-space: nowrap;
                font-size: 0.875rem;
            }

            /* 申请记录和已审核页面的申请内容列适配 */
            #applicationsList td:nth-child(6),
            #approvedList td:nth-child(6) {
                white-space: normal !important;
                min-width: 150px !important;
                max-width: 150px !important;
                width: 150px !important;
                word-break: break-word !important;
            }

            /* 确保申请内容列的文本正常显示 */
            #applicationsList td:nth-child(6) .text-sm,
            #approvedList td:nth-child(6) .text-sm {
                display: block !important;
                white-space: normal !important;
                word-break: break-word !important;
                line-height: 1.4 !important;
                max-height: none !important;
            }

            /* 移动端申请内容列的容器样式 */
            #applicationsList td:nth-child(6) .cursor-pointer,
            #approvedList td:nth-child(6) .cursor-pointer {
                display: block !important;
                width: 100% !important;
                padding: 4px 0 !important;
            }

            /* 移动端申请记录和已审核页面的表格单元格内边距调整 */
            #applicationsList td,
            #approvedList td {
                padding: 8px 6px !important;
            }

            /* 移动端表单适配 */
            .space-y-4 > div, .space-y-6 > div {
                margin-bottom: 1rem;
            }

            /* 移动端新建申请表单优化 */
            #newSection .flex.space-x-4 {
                flex-direction: column;
                gap: 1rem;
            }

            #newSection .flex.space-x-4 > div {
                width: 100% !important;
            }

            /* 移动端表单字段优化 */
            #newSection label {
                font-size: 14px;
                font-weight: 600;
                margin-bottom: 0.5rem;
                color: #374151;
            }

            #newSection input,
            #newSection select,
            #newSection textarea {
                min-height: 44px; /* 触摸友好的最小高度 */
                padding: 0.75rem;
                border-radius: 0.5rem;
                border: 2px solid #e5e7eb;
                transition: border-color 0.2s ease;
            }

            #newSection input:focus,
            #newSection select:focus,
            #newSection textarea:focus {
                border-color: #3b82f6;
                outline: none;
                box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            }

            /* 移动端文件上传区域优化 */
            #newSection .border-dashed {
                padding: 1.5rem;
                border-radius: 0.75rem;
                background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
                border: 2px dashed #cbd5e1;
                text-align: center;
            }

            #newSection .border-dashed:hover {
                border-color: #3b82f6;
                background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
            }

            #newSection #fileInputArea p {
                font-size: 14px;
                margin: 0.25rem 0;
            }

            /* 移动端厂长选择列表优化 */
            #newSection #directorsList {
                max-height: 200px;
                border-radius: 0.5rem;
                border: 2px solid #e5e7eb;
                background-color: #f9fafb;
            }

            /* 移动端按钮优化 */
            #newSection button[type="submit"] {
                width: 100%;
                min-height: 48px;
                font-size: 16px;
                font-weight: 600;
                border-radius: 0.75rem;
                background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
                color: white;
                border: none;
                box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
                transition: all 0.2s ease;
            }

            #newSection button[type="submit"]:hover {
                background: linear-gradient(135deg, #1d4ed8 0%, #1e40af 100%);
                transform: translateY(-1px);
                box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.15);
            }

            #newSection button[type="submit"]:active {
                transform: translateY(0);
            }

            /* 移动端按钮适配 */
            button, select, input[type="date"], input[type="text"], input[type="password"], input[type="email"], textarea {
                font-size: 16px; /* 防止iOS自动缩放 */
            }

            /* 移动端模态框适配 */
            #detailModal .relative, #editModal .relative, #previewModal .relative {
                max-height: 90vh;
                overflow-y: auto;
            }

            /* 移动端用户管理页面适配 */
            #manageUsersSection .grid {
                grid-template-columns: 1fr;
            }

            /* 移动端操作按钮适配 */
            td .space-x-2 {
                display: flex;
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            /* 移动端搜索栏适配 */
            .mb-4.flex.space-x-4 {
                flex-direction: column;
                gap: 0.5rem;
            }

            .mb-4.flex.space-x-4 > * {
                margin: 0.25rem 0;
                width: 100%;
            }

            /* 移动端申请书模板适配 */
            #applicationTemplateModal .p-4.overflow-auto {
                padding: 0.5rem;
                max-height: 85vh;
            }

            .a4-page {
                width: 100%;
                height: auto;
                padding: 10mm;
                transform-origin: top center;
                transform: scale(0.9);
                margin: 0 auto;
                box-sizing: border-box;
                aspect-ratio: 1 / 1.414; /* A4纸张比例 */
            }

            /* 确保表格在移动端也能保持比例 */
            .application-template {
                width: 100%;
                height: 100%;
                font-size: 14px;
                table-layout: fixed;
            }

            .application-template .title {
                font-size: 20px;
                padding: 10px;
                height: 10%;
            }

            .application-template .content {
                height: 40%;
                min-height: 180px;
            }

            .application-template .approval {
                min-height: 90px;
                height: 15%;
            }

            /* 确保签名图片在移动端也能正确显示 */
            .signature-image {
                max-width: 100px;
                max-height: 50px;
            }

            /* 调整下载和关闭按钮 */
            .template-actions {
                padding: 0.5rem;
            }

            .template-actions button {
                padding: 0.5rem 1rem;
                font-size: 14px;
            }

            /* 移动端新建申请表单交互优化 */
            #newSection input[readonly] {
                background-color: #f3f4f6;
                cursor: not-allowed;
                opacity: 0.8;
            }

            /* 移动端表单验证样式 */
            #newSection input:invalid {
                border-color: #ef4444;
                box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
            }

            #newSection input:valid {
                border-color: #10b981;
            }

            /* 移动端文件上传拖拽优化 */
            #newSection .border-dashed.drag-over {
                border-color: #3b82f6;
                background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
                transform: scale(1.02);
            }

            /* 移动端厂长选择项优化 */
            #newSection #directorsList .director-item {
                padding: 0.75rem;
                margin: 0.25rem 0;
                border-radius: 0.5rem;
                background-color: white;
                border: 1px solid #e5e7eb;
                transition: all 0.2s ease;
            }

            #newSection #directorsList .director-item:hover {
                background-color: #f3f4f6;
                border-color: #3b82f6;
            }

            #newSection #directorsList .director-item.selected {
                background-color: #eff6ff;
                border-color: #3b82f6;
            }

            /* 减少移动端动画效果，提高性能 */
            .reduce-animation * {
                transition-duration: 0.1s !important;
                animation-duration: 0.1s !important;
            }

            /* 优化移动端渲染性能 */
            .mobile-device {
                -webkit-text-size-adjust: 100%;
                -webkit-font-smoothing: antialiased;
            }

            /* 移动端新建申请表单性能优化 */
            .mobile-device #newSection {
                transform: translateZ(0);
                backface-visibility: hidden;
            }

            .mobile-device #newSection input,
            .mobile-device #newSection select,
            .mobile-device #newSection textarea {
                will-change: border-color, box-shadow;
            }

            /* 使用硬件加速 */
            .mobile-device .a4-page,
            .mobile-device #applicationTemplateModal,
            .mobile-device #detailModal,
            .mobile-device #editModal,
            .mobile-device #previewModal {
                transform: translateZ(0);
                backface-visibility: hidden;
                perspective: 1000px;
            }

            /* 优化移动端滚动性能 */
            .mobile-device .overflow-auto,
            .mobile-device .overflow-y-auto,
            .mobile-device .overflow-x-auto {
                -webkit-overflow-scrolling: touch;
                scroll-behavior: auto;
            }

            /* 移动端PDF预览样式 */
            #mobilePdfPreview {
                display: flex;
                flex-direction: column;
            }

            #pdfNavigation {
                flex-shrink: 0;
                background: linear-gradient(135deg, #f3f4f6 0%, #e5e7eb 100%);
                border-bottom: 2px solid #d1d5db;
                box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            #pageInfo {
                font-weight: 600;
                color: #374151;
                background-color: rgba(255, 255, 255, 0.8);
                padding: 0.25rem 0.75rem;
                border-radius: 1rem;
                border: 1px solid #d1d5db;
            }

            #pdfContainer {
                flex: 1;
                background: #f9fafb;
                overflow-y: auto;
                overflow-x: hidden;
                -webkit-overflow-scrolling: touch;
                touch-action: pan-y pinch-zoom;
                position: relative;
            }

            #pdfCanvas {
                display: block;
                margin: 0 auto;
                max-width: 100%;
                height: auto;
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
                background: white;
                touch-action: pan-y pinch-zoom;
                image-rendering: -webkit-optimize-contrast;
                image-rendering: crisp-edges;
                image-rendering: pixelated;
            }

            /* 滑动提示样式 */
            #swipeHint {
                z-index: 20;
                pointer-events: none;
                user-select: none;
            }

            #pdfLoadingIndicator {
                background: rgba(255, 255, 255, 0.95);
                backdrop-filter: blur(4px);
            }

            #pdfLoadingIndicator .animate-spin {
                border-color: #3b82f6;
                border-top-color: transparent;
            }
        }

        /* 添加媒体查询，针对特小屏幕设备 */
        @media (max-width: 480px) {
            body {
                font-size: 14px;
            }

            .container {
                padding-left: 0.5rem;
                padding-right: 0.5rem;
            }

            /* 移动端新建申请页面容器优化 */
            #newSection {
                margin: 0.5rem;
                padding: 1rem;
                border-radius: 0.75rem;
            }

            #newSection h2 {
                font-size: 1.25rem;
                margin-bottom: 1.5rem;
                text-align: center;
                color: #1f2937;
            }

            /* 移动端表单分组优化 */
            #newSection .space-y-4 {
                gap: 1.5rem;
            }

            /* 移动端文件列表优化 */
            #newSection #fileList .flex {
                flex-direction: column;
                gap: 0.5rem;
                padding: 0.75rem;
                background-color: #f3f4f6;
                border-radius: 0.5rem;
                margin-bottom: 0.5rem;
            }

            #newSection #fileList button {
                align-self: flex-end;
                padding: 0.25rem 0.5rem;
                font-size: 12px;
                min-height: 32px;
            }

            h2 {
                font-size: 1.25rem;
            }

            /* 极小屏幕表格适配 */
            th, td {
                padding: 0.5rem 0.25rem;
            }

            /* 调整表格内容显示 - 不隐藏用户管理页面的表格列 */
            #historySection td:nth-child(3), #historySection th:nth-child(3),
            #historySection td:nth-child(4), #historySection th:nth-child(4),
            #pendingApprovalSection td:nth-child(3), #pendingApprovalSection th:nth-child(3),
            #pendingApprovalSection td:nth-child(4), #pendingApprovalSection th:nth-child(4),
            #approvedSection td:nth-child(3), #approvedSection th:nth-child(3),
            #approvedSection td:nth-child(4), #approvedSection th:nth-child(4) {
                display: none;
            }

            /* 用户管理页面的表格适配 */
            #manageUsersSection .overflow-x-auto {
                position: relative;
                overflow-x: auto;
                -webkit-overflow-scrolling: touch;
                margin-bottom: 1rem;
            }

            /* 增强滑动提示的可见性 */
            .table-scroll-hint {
                background-color: rgba(255, 255, 255, 0.8);
                padding: 0.25rem;
                margin-bottom: 0.5rem;
                border-radius: 0.25rem;
                box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
            }

            /* 添加滑动指示器 */
            .table-scroll-indicator {
                position: absolute;
                bottom: 0.5rem;
                right: 0.5rem;
                background-color: rgba(59, 130, 246, 0.7);
                color: white;
                padding: 0.25rem 0.5rem;
                border-radius: 1rem;
                font-size: 0.75rem;
                pointer-events: none;
                opacity: 0;
                transition: opacity 0.3s;
                z-index: 10;
            }

            /* 当表格可以滚动时显示指示器 */
            .overflow-x-auto:hover .table-scroll-indicator,
            .overflow-x-auto:active .table-scroll-indicator {
                opacity: 1;
            }

            /* 移动端待审核卡片样式 */
            #pendingApprovalTable {
                display: none;
            }

            #pendingApprovalCards {
                display: block !important;
            }

            .pending-card {
                border-radius: 0.5rem;
                overflow: hidden;
            }

            .pending-card-header {
                background-color: #f3f4f6;
                padding: 0.75rem;
                border-bottom: 1px solid #e5e7eb;
            }

            .pending-card-body {
                padding: 1rem;
            }

            .pending-card-footer {
                padding: 0.75rem;
                border-top: 1px solid #e5e7eb;
                display: flex;
                justify-content: space-between;
            }

            .pending-card-item {
                margin-bottom: 0.75rem;
                display: flex;
                flex-direction: column;
            }

            .pending-card-label {
                font-size: 0.75rem;
                color: #6b7280;
                margin-bottom: 0.25rem;
            }

            .pending-card-value {
                font-size: 0.875rem;
                font-weight: 500;
            }

            .pending-card-content {
                background-color: #f9fafb;
                padding: 0.75rem;
                border-radius: 0.25rem;
                margin: 0.5rem 0;
                max-height: 120px;
                overflow-y: auto;
                font-size: 0.9rem;
                line-height: 1.4;
                color: #374151;
                border-left: 3px solid #e5e7eb;
                transition: all 0.2s ease;
            }

            .pending-card-content:hover {
                background-color: #f3f4f6;
                border-left-color: #3b82f6;
            }

            /* 移动端附件预览优化 */
            .flex.items-center.justify-between.bg-gray-50.p-2.rounded,
            .flex.items-center.justify-between.bg-gray-100.p-2.rounded,
            .flex.items-center.justify-between.p-2.bg-gray-50.rounded-md {
                flex-direction: column;
                align-items: flex-start;
                gap: 0.5rem;
            }

            /* 文件名显示优化 */
            .flex.items-center.justify-between.bg-gray-50.p-2.rounded span,
            .flex.items-center.justify-between.bg-gray-100.p-2.rounded span,
            .flex.items-center.justify-between.p-2.bg-gray-50.rounded-md span {
                word-break: break-all;
                white-space: normal;
                width: 100%;
                display: block;
                padding-right: 0.5rem;
            }

            /* 附件操作按钮优化 */
            .flex.items-center.justify-between.bg-gray-50.p-2.rounded .flex.space-x-2,
            .flex.items-center.justify-between.bg-gray-100.p-2.rounded .flex.space-x-2 {
                width: 100%;
                display: flex;
                flex-direction: row;
                justify-content: space-between;
            }

            /* 详情模态框内容优化 */
            #detailContent .space-y-2 {
                margin-bottom: 1rem;
            }

            /* 附件列表项间距 */
            .ml-4.space-y-2 > div {
                margin-bottom: 0.75rem;
            }

            /* 移动端申请书模板适配（特小屏幕） */
            .a4-page {
                transform: scale(0.7);
                padding: 8mm;
                aspect-ratio: 1 / 1.414; /* 保持A4纸张比例 */
            }

            .application-template {
                font-size: 12px;
            }

            .application-template .title {
                font-size: 18px;
                padding: 8px;
            }

            .application-template .content {
                height: 40%;
                min-height: 160px;
            }

            .application-template .approval {
                min-height: 80px;
                height: 15%;
            }

            .signature-image {
                max-width: 80px;
                max-height: 40px;
            }

            /* 确保模态框在小屏幕上正确显示 */
            #applicationTemplateModal .p-4.border-b h2 {
                font-size: 1.2rem;
            }

            /* 调整下载和关闭按钮 */
            .template-actions button {
                padding: 0.4rem 0.8rem;
                font-size: 12px;
            }
        }

        .signature-image {
            max-width: 120px;
            max-height: 60px;
            margin-bottom: 5px;
        }

        .application-template.mobile .signature-image {
            max-width: 100px;
            max-height: 50px;
        }

        .application-template.mobile-small .signature-image {
            max-width: 80px;
            max-height: 40px;
        }

        .approval-date {
            font-size: 12px;
            color: #666;
            text-align: center;
        }

        .application-template.mobile .approval-date {
            font-size: 11px;
        }

        .application-template.mobile-small .approval-date {
            font-size: 10px;
        }

        .no-signature {
            color: #999;
            font-style: italic;
            font-size: 12px;
            margin-bottom: 5px;
        }

        .application-template.mobile .no-signature {
            font-size: 11px;
        }

        .application-template.mobile-small .no-signature {
            font-size: 10px;
        }

        /* 申请书模板样式优化 */
        .application-template {
            width: 100%;
            font-family: "Microsoft YaHei", "Segoe UI", sans-serif;
            text-rendering: optimizeLegibility;
            -webkit-font-smoothing: antialiased;
            border-collapse: collapse;
            border: 1px solid #000;
        }

        .application-template th,
        .application-template td {
            border: 1px solid #000;
            padding: 8px;
        }

        .application-template .title {
            font-weight: bold;
            text-align: center;
            letter-spacing: 2px;
        }

        .application-template .label {
            font-weight: bold;
            text-align: center;
            background-color: #f5f5f5;
        }

        .application-template .content {
            line-height: 1.5;
            text-align: justify;
            padding: 10px;
            vertical-align: top;
        }

        .application-template .applicant {
            text-align: right;
            padding-right: 20px;
            font-weight: normal;
        }

        .application-template .approval-comment {
            margin-bottom: 8px;
            font-weight: 400;
        }

        /* 打印优化 */
        @media print {
            .application-template {
                font-size: 12pt;
                border: 1pt solid #000;
                width: 100%;
                max-width: 100%;
                margin: 0;
                padding: 0;
                box-shadow: none;
                text-rendering: optimizeLegibility;
                -webkit-font-smoothing: antialiased;
            }

            .application-template th,
            .application-template td {
                border: 1pt solid #000;
                padding: 8pt;
            }

            .application-template .title {
                font-size: 18pt;
                font-weight: bold;
                padding: 10pt;
            }

            .application-template .content {
                font-size: 12pt;
                line-height: 1.5;
            }

            .application-template .label {
                font-weight: bold;
                background-color: #f8f8f8 !important;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }

            .signature-image {
                max-width: 120px;
                max-height: 60px;
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }

            .signature-image {
                print-color-adjust: exact;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>

    <!-- 添加视口元标记以确保正确缩放 -->
    <script>
        // 检测移动设备并调整布局
        function checkMobileDevice() {
            // 使用缓存避免重复检测
            if (window.isMobileChecked) return;
            window.isMobileChecked = true;

            // 使用更高效的方式检测移动设备
            const isMobile = window.innerWidth <= 768 || /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

            if (isMobile) {
                document.body.classList.add('mobile-device');

                // 延迟处理非关键UI元素
                setTimeout(() => {
                    // 为表格添加横向滚动提示
                    const tables = document.querySelectorAll('.overflow-x-auto');
                    tables.forEach(tableContainer => {
                        // 检查是否已经有提示
                        if (!tableContainer.querySelector('.table-scroll-hint')) {
                            const tableWrapper = document.createElement('div');
                            tableWrapper.className = 'table-scroll-hint';
                            tableWrapper.innerHTML = '<p class="text-xs text-gray-500 text-center mb-2">← 左右滑动查看更多 →</p>';
                            tableContainer.insertBefore(tableWrapper, tableContainer.firstChild);

                            // 添加滚动指示器
                            const scrollIndicator = document.createElement('div');
                            scrollIndicator.className = 'table-scroll-indicator';
                            scrollIndicator.textContent = '滑动查看';
                            tableContainer.appendChild(scrollIndicator);

                            // 使用被动事件监听器提高滚动性能
                            tableContainer.addEventListener('scroll', function() {
                                const maxScroll = tableContainer.scrollWidth - tableContainer.clientWidth;
                                if (maxScroll > 10) {
                                    scrollIndicator.style.opacity = '1';
                                    if (tableContainer.scrollLeft >= maxScroll - 5) {
                                        scrollIndicator.style.opacity = '0';
                                    }
                                } else {
                                    scrollIndicator.style.opacity = '0';
                                }
                            }, { passive: true });
                        }
                    });

                    // 优化移动端图片加载
                    document.querySelectorAll('img').forEach(img => {
                        if (!img.hasAttribute('loading')) {
                            img.setAttribute('loading', 'lazy');
                        }

                        // 对于签名图片进行优化
                        if (img.classList.contains('signature-image') && img.src) {
                            optimizeImage(img);
                        }
                    });

                    // 减少移动端动画效果
                    document.body.classList.add('reduce-animation');
                }, 100);
            }
        }

        // 会话监控变量
        let sessionTimer = null;
        let warningTimer = null;
        const SESSION_TIMEOUT = 10 * 60 * 1000; // 10分钟，单位毫秒
        const WARNING_BEFORE_TIMEOUT = 1 * 60 * 1000; // 在超时前1分钟显示警告

        // 创建会话超时警告模态框
        function createSessionTimeoutWarning() {
            // 检查是否已存在
            if (document.getElementById('sessionTimeoutWarning')) {
                return;
            }

            // 创建警告模态框
            const modal = document.createElement('div');
            modal.id = 'sessionTimeoutWarning';
            modal.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 hidden';
            modal.innerHTML = `
                <div class="bg-white p-6 rounded-lg shadow-lg w-full max-w-md">
                    <div class="flex justify-center mb-4">
                        <div class="h-12 w-12 bg-red-600 rounded-full flex items-center justify-center shadow-md">
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-8 w-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                            </svg>
                        </div>
                    </div>
                    <h2 class="text-xl font-bold mb-4 text-center">会话即将超时</h2>
                    <p class="text-center mb-6">由于长时间无活动，您的会话将在<span id="timeoutCountdown">60</span>秒后自动退出。</p>
                    <div class="flex space-x-4 justify-center">
                        <button id="continueSession" class="bg-blue-600 text-white px-6 py-2 rounded-md hover:bg-blue-700">继续操作</button>
                        <button id="logoutNow" class="bg-gray-300 text-gray-800 px-6 py-2 rounded-md hover:bg-gray-400">立即退出</button>
                    </div>
                </div>
            `;

            // 添加到文档
            document.body.appendChild(modal);

            // 添加事件监听器
            document.getElementById('continueSession').addEventListener('click', function() {
                hideSessionWarning();
                updateUserActivity();
            });

            document.getElementById('logoutNow').addEventListener('click', function() {
                hideSessionWarning();
                logout();
            });
        }

        // 显示会话超时警告
        function showSessionWarning() {
            createSessionTimeoutWarning();
            const modal = document.getElementById('sessionTimeoutWarning');
            modal.classList.remove('hidden');
            document.body.classList.add('modal-open');

            // 启动倒计时
            let countdown = 60; // 60秒倒计时
            const countdownElement = document.getElementById('timeoutCountdown');
            countdownElement.textContent = countdown;

            const countdownInterval = setInterval(function() {
                countdown--;
                if (countdown <= 0) {
                    clearInterval(countdownInterval);
                    return;
                }
                countdownElement.textContent = countdown;
            }, 1000);
        }

        // 隐藏会话超时警告
        function hideSessionWarning() {
            const modal = document.getElementById('sessionTimeoutWarning');
            if (modal) {
                modal.classList.add('hidden');
                document.body.classList.remove('modal-open');
            }
        }

        // 启动会话监控
        function startSessionMonitoring() {
            // 清除可能存在的旧计时器
            if (sessionTimer) {
                clearTimeout(sessionTimer);
            }

            if (warningTimer) {
                clearTimeout(warningTimer);
            }

            // 设置警告计时器
            warningTimer = setTimeout(function() {
                console.log('会话即将超时，显示警告');
                showSessionWarning();
            }, SESSION_TIMEOUT - WARNING_BEFORE_TIMEOUT);

            // 设置会话超时计时器
            sessionTimer = setTimeout(function() {
                console.log('会话超时，自动登出');
                hideSessionWarning();
                logout();
            }, SESSION_TIMEOUT);

            // 添加用户活动监听器（如果尚未添加）
            if (!window.sessionMonitoringActive) {
                // 监听用户活动事件
                const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click', 'keydown'];

                activityEvents.forEach(function(eventName) {
                    document.addEventListener(eventName, updateUserActivity, true);
                });

                window.sessionMonitoringActive = true;
                console.log('会话监控已启动');
            }
        }

        // 更新用户活动时间
        function updateUserActivity() {
            if (sessionStorage.getItem('isLoggedIn') !== 'true') {
                return; // 如果用户未登录，不更新活动时间
            }

            // 更新最后活动时间
            const currentTime = Date.now();
            sessionStorage.setItem('lastActivity', currentTime.toString());

            // 隐藏警告（如果显示）
            hideSessionWarning();

            // 重置计时器
            if (sessionTimer) {
                clearTimeout(sessionTimer);
            }

            if (warningTimer) {
                clearTimeout(warningTimer);
            }

            // 设置新的警告计时器
            warningTimer = setTimeout(function() {
                console.log('会话即将超时，显示警告');
                showSessionWarning();
            }, SESSION_TIMEOUT - WARNING_BEFORE_TIMEOUT);

            // 设置新的会话超时计时器
            sessionTimer = setTimeout(function() {
                console.log('会话超时，自动登出');
                hideSessionWarning();
                logout();
            }, SESSION_TIMEOUT);
        }

        // 在DOM加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            // 检查会话状态
            const isLoggedIn = sessionStorage.getItem('isLoggedIn') === 'true';

            if (isLoggedIn) {
                // 检查会话是否已过期（10分钟无活动）
                const lastActivity = parseInt(sessionStorage.getItem('lastActivity') || '0');
                const currentTime = Date.now();
                const inactiveTime = currentTime - lastActivity;
                const maxInactiveTime = 10 * 60 * 1000; // 10分钟，单位毫秒

                if (inactiveTime > maxInactiveTime) {
                    console.log('会话已过期，需要重新登录');
                    // 会话已过期，执行登出操作
                    logout();
                    return;
                }

                // 会话有效，更新最后活动时间
                sessionStorage.setItem('lastActivity', currentTime.toString());

                // 启动会话监控
                startSessionMonitoring();

                // 从sessionStorage获取用户信息
                currentUser = sessionStorage.getItem('username');
                currentRole = sessionStorage.getItem('role');
                currentDepartment = sessionStorage.getItem('department');

                // 显示主应用界面但不处理页面导航（页面导航在下面单独处理）
                document.getElementById('loginPage').classList.add('hidden');
                document.getElementById('mainApp').classList.remove('hidden');

                // 在移动端默认隐藏侧边栏，PC端不需要显示侧边栏
                const sideMenu = document.getElementById('sideMenu');
                if (window.innerWidth < 768) {
                    sideMenu.classList.add('-translate-x-full');
                    document.getElementById('menuOverlay').classList.add('hidden');
                }

                // 确保PC端顶部导航菜单正确显示
                if (window.innerWidth >= 768) {
                    const topNavMenu = document.querySelector('.hidden.md\\:block');
                    if (topNavMenu) {
                        topNavMenu.classList.remove('hidden');
                        topNavMenu.style.display = 'flex';
                    }
                }

                // 初始化文件上传功能
                initFileUpload();

                // 设置申请日期默认为当前日期
                const today = new Date().toISOString().split('T')[0];
                document.getElementById('applyDate').value = today;

                // 设置申请人姓名为当前用户
                document.getElementById('applicant').value = currentUser;

                // 设置申请部门为当前用户的部门
                if (currentDepartment) {
                    const departmentInput = document.getElementById('department');
                    if (departmentInput) {
                        departmentInput.value = currentDepartment;
                    }
                }

                updateUserDisplay();
                updateNavButtons();

                // 加载应用数据
                loadApplications().then(() => {
                    // 确保签名信息已被提取
                    if (!allUsers || allUsers.length === 0) {
                        extractSignaturesFromApplications();
                    }
                    console.log('页面加载完成，已提取签名信息:', allUsers);
                });

                loadApprovers();

                // 确保加载所有用户数据，以便正确显示电子签名
                if (currentRole === 'admin') {
                    loadUsers();
                } else {
                    loadAllUsers();
                }

                // 根据用户角色和sessionStorage中保存的section显示正确的内容
                // 注意：只使用sessionStorage，确保浏览器关闭后页面状态被清除
                let savedSection = sessionStorage.getItem('currentSection');

                // 检查是否是首次登录（没有保存的页面信息）
                // 浏览器关闭后重新打开时，sessionStorage被清除，视为首次登录
                const isFirstLogin = !savedSection;

                // 根据用户角色设置默认首页（仅在首次登录时使用）
                let defaultSection;
                if (currentRole === 'admin') {
                    // 管理员默认显示用户管理页面
                    defaultSection = 'manageUsers';
                } else if (['director', 'chief', 'manager', 'ceo'].includes(currentRole)) {
                    // 所有审批人默认显示待审核页面
                    defaultSection = 'pendingApproval';
                } else {
                    // 普通用户默认显示申请记录页面
                    defaultSection = 'history';
                }

                // 如果是首次登录或没有保存的页面，使用默认页面；否则保持当前页面
                const targetSection = isFirstLogin ? defaultSection : savedSection;

                // 只保存到sessionStorage，确保浏览器关闭后页面状态被清除
                sessionStorage.setItem('currentSection', targetSection);

                showSection(targetSection);


            } else {
                showLoginForm();
            }

            // 添加角色变更事件监听器
            document.getElementById('newUserRole').addEventListener('change', toggleNewUserDepartment);

            // 添加分页按钮事件监听器
            document.getElementById('prevPageBtn').addEventListener('click', function() {
                if (currentUserPage > 1) {
                    currentUserPage--;
                    renderUsersList();
                }
            });

            document.getElementById('nextPageBtn').addEventListener('click', function() {
                const totalPages = Math.ceil(allUsers.length / usersPerPage);
                if (currentUserPage < totalPages) {
                    currentUserPage++;
                    renderUsersList();
                }
            });

            // 添加用户搜索框回车键事件监听器
            document.getElementById('userSearchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchUsers();
                }
            });

            // 添加搜索框回车键触发搜索
            document.getElementById('searchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchApplications();
                }
            });

            // 添加已审核页面搜索框回车键触发搜索
            document.getElementById('approvedSearchInput').addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    e.preventDefault();
                    searchApprovedApplications();
                }
            });

            // 处理搜索框清除按钮
            function setupSearchClearButton(inputId, buttonId) {
                const input = document.getElementById(inputId);
                const clearButton = document.getElementById(buttonId);

                if (!input || !clearButton) return;

                // 输入框内容变化时显示/隐藏清除按钮
                input.addEventListener('input', function() {
                    if (this.value) {
                        clearButton.classList.remove('hidden');
                    } else {
                        clearButton.classList.add('hidden');

                        // 当输入框被清空时（例如用户按下Backspace或Delete键删除所有内容）
                        // 根据输入框ID判断是哪个搜索框，并触发相应的搜索重置
                        if (inputId === 'searchInput') {
                            updateHistoryList(); // 重置申请记录页面的搜索结果
                        } else if (inputId === 'approvedSearchInput') {
                            updateApprovedList(); // 重置已审核页面的搜索结果
                        }
                    }
                });

                // 点击清除按钮时清空输入框并重置搜索结果
                clearButton.addEventListener('click', function() {
                    input.value = '';
                    clearButton.classList.add('hidden');

                    // 根据输入框ID判断是哪个搜索框，并触发相应的搜索重置
                    if (inputId === 'searchInput') {
                        updateHistoryList(); // 重置申请记录页面的搜索结果
                    } else if (inputId === 'approvedSearchInput') {
                        updateApprovedList(); // 重置已审核页面的搜索结果
                    }

                    input.focus(); // 保持焦点在输入框
                });

                // 初始检查输入框是否有内容
                if (input.value) {
                    clearButton.classList.remove('hidden');
                }
            }

            // 设置申请记录页面的搜索框清除按钮
            setupSearchClearButton('searchInput', 'clearSearchInput');

            // 设置已审核页面的搜索框清除按钮
            setupSearchClearButton('approvedSearchInput', 'clearApprovedSearchInput');

            // 键盘快捷键支持已移除，恢复浏览器默认刷新行为
            // 用户现在可以使用 Ctrl+R 或 F5 进行正常的浏览器页面刷新

            // 检测移动设备并调整布局
            checkMobileDevice();

            // 添加菜单切换事件监听
            document.getElementById('menuToggleBtn').addEventListener('click', toggleMenu);
            document.getElementById('closeMenuBtn').addEventListener('click', toggleMenu);

            // 添加卡片导航按钮事件监听
            document.getElementById('prevCardBtn').addEventListener('click', showPrevCard);
            document.getElementById('nextCardBtn').addEventListener('click', showNextCard);

            // 添加窗口大小改变事件监听器
            window.addEventListener('resize', function() {
                const sideMenu = document.getElementById('sideMenu');

                if (window.innerWidth >= 768) {
                    // PC端隐藏侧边栏
                    sideMenu.classList.add('-translate-x-full');
                    document.body.classList.remove('overflow-hidden');
                    document.getElementById('menuOverlay').classList.add('hidden');

                    // 确保PC端顶部导航菜单正确显示
                    const topNavMenu = document.querySelector('.hidden.md\\:block');
                    if (topNavMenu) {
                        topNavMenu.classList.remove('hidden');
                        topNavMenu.style.display = 'block';
                    }

                    // 确保PC端用户信息和退出按钮正确显示
                    const userInfoSection = document.querySelector('.hidden.md\\:flex');
                    if (userInfoSection) {
                        userInfoSection.classList.remove('hidden');
                        userInfoSection.style.display = 'flex';
                    }
                } else {
                    // 移动端默认隐藏侧边栏
                    sideMenu.classList.add('-translate-x-full');
                    document.getElementById('menuOverlay').classList.add('hidden');
                }
            });

            // 检查是否需要恢复到待审核部分（用于审批后刷新页面）
            // 注意：这个逻辑只在特定情况下执行，不会影响正常的页面导航逻辑
            // 使用sessionStorage确保浏览器关闭后标记被清除
            const lastActiveSection = sessionStorage.getItem('lastActiveSection');
            if (lastActiveSection === '待审核') {
                // 清除标记，防止下次加载时再次跳转
                sessionStorage.removeItem('lastActiveSection');
                // 延迟执行，确保页面已完全加载
                setTimeout(() => {
                    showSection('pendingApproval');
                    // 更新存储状态
                    sessionStorage.setItem('currentSection', 'pendingApproval');
                }, 100);
            }

            // 初始化修改密码相关事件监听（避免重复绑定）
            const changePasswordBtnMain = document.getElementById('changePasswordBtn');
            const cancelChangePasswordMain = document.getElementById('cancelChangePassword');
            const changePasswordFormMain = document.getElementById('changePasswordForm');

            if (changePasswordBtnMain && !changePasswordBtnMain.hasAttribute('data-listener-added')) {
                changePasswordBtnMain.addEventListener('click', showChangePasswordModal);
                changePasswordBtnMain.setAttribute('data-listener-added', 'true');
            }

            if (cancelChangePasswordMain && !cancelChangePasswordMain.hasAttribute('data-listener-added')) {
                cancelChangePasswordMain.addEventListener('click', hideChangePasswordModal);
                cancelChangePasswordMain.setAttribute('data-listener-added', 'true');
            }

            if (changePasswordFormMain && !changePasswordFormMain.hasAttribute('data-listener-added')) {
                changePasswordFormMain.addEventListener('submit', handleChangePassword);
                changePasswordFormMain.setAttribute('data-listener-added', 'true');
            }

            // 初始化导出日期范围选择器
            initializeExportDateRange();
        });

        // 初始化导出日期范围选择器
        function initializeExportDateRange() {
            const toggleBtn = document.getElementById('toggleExportDateRange');
            const container = document.getElementById('exportDateRangeContainer');
            const startDateInput = document.getElementById('exportStartDate');
            const endDateInput = document.getElementById('exportEndDate');

            // 设置默认日期范围（当月）
            const today = new Date();
            const firstDayOfMonth = new Date(today.getFullYear(), today.getMonth(), 1);

            startDateInput.value = firstDayOfMonth.toISOString().split('T')[0];
            endDateInput.value = today.toISOString().split('T')[0];

            // 切换日期范围选择器的显示/隐藏
            toggleBtn.addEventListener('click', function() {
                if (container.classList.contains('hidden')) {
                    container.classList.remove('hidden');
                    toggleBtn.classList.add('bg-blue-800');
                    toggleBtn.setAttribute('title', '隐藏日期筛选');
                } else {
                    container.classList.add('hidden');
                    toggleBtn.classList.remove('bg-blue-800');
                    toggleBtn.setAttribute('title', '显示日期筛选');
                }
            });
        }
    </script>

    <!-- 修复PC端顶部导航菜单显示问题的内联脚本 -->
    <script>
        // 在页面加载完成后立即执行
        window.addEventListener('load', function() {
            // 确保PC端顶部导航菜单正确显示
            if (window.innerWidth >= 768) {
                const topNavMenu = document.querySelector('.hidden.md\\:block');
                if (topNavMenu) {
                    topNavMenu.classList.remove('hidden');
                    topNavMenu.style.display = 'block';
                }

                // 确保PC端用户信息和退出按钮正确显示
                const userInfoSection = document.querySelector('.hidden.md\\:flex');
                if (userInfoSection) {
                    userInfoSection.classList.remove('hidden');
                    userInfoSection.style.display = 'flex';
                }
            }
        });
    </script>

    <!-- 申请内容弹窗显示功能 -->
    <script src="js/content-modal.js"></script>

    <!-- 模态框高度控制功能 -->
    <script src="js/modal-height-control.js"></script>

    <!-- 模态框布局优化功能 -->
    <script src="js/modal-layout-optimizer.js"></script>

    <!-- 响应式模态框布局功能 -->
    <script src="js/responsive-modal-layout.js"></script>
</body>
</html>